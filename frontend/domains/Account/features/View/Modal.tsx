import { LanguageSettings } from '@zeal/uikit/Language'
import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { UserAReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { ConnectionMap } from '@zeal/domains/DApp/domains/ConnectionState'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Mode } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { Settings } from '@zeal/domains/Settings/features/Settings'
import {
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    state: State
    earnTakerMetrics: EarnTakerMetrics
    accountsMap: AccountsMap
    cardConfig: CardConfig
    connections: ConnectionMap
    currencyHiddenMap: CurrencyHiddenMap
    customCurrencyMap: CustomCurrencyMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    encryptedPassword: string
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    isEthereumNetworkFeeWarningSeen: boolean
    keyStoreMap: KeyStoreMap
    mode: Mode
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    notificationsConfig: NotificationsConfig
    portfolioMap: PortfolioMap
    selectedAccount: Account
    installationCampaign: string | null
    sessionPassword: string
    userAReferralConfig: UserAReferralConfig
    languageSettings: LanguageSettings
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof Settings>

export type State = { type: 'closed' } | { type: 'settings' }

export const Modal = ({
    state,

    earnTakerMetrics,
    portfolioMap,
    accountsMap,
    cardConfig,
    connections,
    currencyHiddenMap,
    customCurrencyMap,
    defaultCurrencyConfig,
    encryptedPassword,
    feePresetMap,
    gasCurrencyPresetMap,
    userAReferralConfig,
    installationCampaign,
    installationId,
    isEthereumNetworkFeeWarningSeen,
    keyStoreMap,
    mode,
    networkMap,
    networkRPCMap,
    notificationsConfig,
    selectedAccount,
    sessionPassword,
    languageSettings,
    experimentalMode,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'settings':
            return (
                <UIModal>
                    <Settings
                        experimentalMode={experimentalMode}
                        languageSettings={languageSettings}
                        userAReferralConfig={userAReferralConfig}
                        mode={mode}
                        installationCampaign={installationCampaign}
                        earnTakerMetrics={earnTakerMetrics}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        customCurrencyMap={customCurrencyMap}
                        networkMap={networkMap}
                        sessionPassword={sessionPassword}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        installationId={installationId}
                        networkRPCMap={networkRPCMap}
                        currencyHiddenMap={currencyHiddenMap}
                        encryptedPassword={encryptedPassword}
                        accountsMap={accountsMap}
                        connections={connections}
                        feePresetMap={feePresetMap}
                        portfolioMap={portfolioMap}
                        selectedAccount={selectedAccount}
                        notificationsConfig={notificationsConfig}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        keyStoreMap={keyStoreMap}
                        cardConfig={cardConfig}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
