import { FormattedMessage } from 'react-intl'

import { Avatar } from '@zeal/uikit/Avatar'
import { RechargeLightning } from '@zeal/uikit/Icon/RechargeLightning'
import { ListItem } from '@zeal/uikit/ListItem'

import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { convertStableCoinCurrencyToFiatCurrency } from '@zeal/domains/Currency/helpers/convertStableCoinCurrencyToFiatCurrency'
import { CardRechargeEnabled, ConfiguredEarn } from '@zeal/domains/Earn'
import { FormattedMoneyCompact } from '@zeal/domains/Money/components/FormattedMoneyCompact'

type Msg = {
    type: 'on_enabled_recharge_list_item_clicked'
    earn: ConfiguredEarn
    cardRecharge: CardRechargeEnabled
}

type Props = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    earn: ConfiguredEarn
    cardRecharge: CardRechargeEnabled
    onMsg: (msg: Msg) => void
}

export const EnabledRechargeListItem = ({
    earn,
    cardConfig,
    cardRecharge,
    onMsg,
}: Props) => {
    return (
        <ListItem
            size="regular"
            aria-current={false}
            avatar={({ size }) => (
                <Avatar size={size} variant="square">
                    <RechargeLightning size={size} color="teal40" />
                </Avatar>
            )}
            primaryText={
                <FormattedMessage
                    id="card.settings.setAutoRecharge"
                    defaultMessage="Set auto-recharge"
                />
            }
            shortText={
                <FormattedMessage
                    id="card.settings.targetBalance"
                    defaultMessage="Target balance: {threshold}"
                    values={{
                        threshold: (
                            <FormattedMoneyCompact
                                money={{
                                    amount: cardRecharge.threshold,
                                    currency:
                                        convertStableCoinCurrencyToFiatCurrency(
                                            {
                                                cryptoCurrency:
                                                    cardConfig.currency,
                                            }
                                        ),
                                }}
                            />
                        ),
                    }}
                />
            }
            onClick={() =>
                onMsg({
                    type: 'on_enabled_recharge_list_item_clicked',
                    cardRecharge,
                    earn,
                })
            }
        />
    )
}
