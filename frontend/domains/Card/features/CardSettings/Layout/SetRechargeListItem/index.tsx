import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { Earn } from '@zeal/domains/Earn'

import { DisabledRechargeListItem } from './DisabledRechargeListItem'
import { EnabledRechargeListItem } from './EnabledRechargeListItem'

type Msg =
    | MsgOf<typeof EnabledRechargeListItem>
    | MsgOf<typeof DisabledRechargeListItem>

type Props = {
    earn: Earn
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    onMsg: (msg: Msg) => void
}
export const SetRechargeListItem = ({ earn, cardConfig, onMsg }: Props) => {
    switch (earn.type) {
        case 'not_configured':
            return <DisabledRechargeListItem onMsg={onMsg} />
        case 'configured':
            switch (earn.cardRecharge.type) {
                case 'recharge_disabled':
                    return <DisabledRechargeListItem onMsg={onMsg} />
                case 'recharge_enabled':
                    return (
                        <EnabledRechargeListItem
                            cardRecharge={earn.cardRecharge}
                            cardConfig={cardConfig}
                            earn={earn}
                            onMsg={onMsg}
                        />
                    )

                /* istanbul ignore next */
                default:
                    return notReachable(earn.cardRecharge)
            }

        /* istanbul ignore next */
        default:
            return notReachable(earn)
    }
}
