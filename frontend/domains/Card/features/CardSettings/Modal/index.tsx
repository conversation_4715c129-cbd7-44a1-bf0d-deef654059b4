import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import { ZealMobilePlatform } from '@zeal/toolkit/OS/ZealPlatform'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardBalance,
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
    NotActivatedPhysicalCard,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { DisconnectCardPopup } from '@zeal/domains/Card/components/DisconnectCardPopup'
import { ActivatePhysicalCard } from '@zeal/domains/Card/features/ActivatePhysicalCard'
import { AddToWallet } from '@zeal/domains/Card/features/AddToWallet'
import { CardViewPin } from '@zeal/domains/Card/features/CardViewPin'
import { SwitchCard } from '@zeal/domains/Card/features/SwitchCard'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import {
    CardRechargeEnabled,
    ConfiguredEarn,
    Earn,
    EarnTakerMetrics,
} from '@zeal/domains/Earn'
import { ConfigureRecharge } from '@zeal/domains/Earn/features/ConfigureRecharge'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { ViewCardSafeAddress } from './ViewCardSafeAddress'

type Props = {
    state: State
    cardReadonlySigner: Account
    earn: Earn
    earnTakerMetrics: EarnTakerMetrics
    cardBalance: CardBalance | null
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    feePresetMap: FeePresetMap
    customCurrencyMap: CustomCurrencyMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    encryptedPassword: string
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    keyStore: CardSlientSignKeyStore
    installationId: string
    onMsg: (msg: Msg) => void
}

export type State =
    | { type: 'view_pin' }
    | { type: 'disconnect_card' }
    | { type: 'view_card_safe_address' }
    | { type: 'switch_card' }
    | { type: 'closed' }
    | { type: 'wallet_pay_flow'; platform: ZealMobilePlatform }
    | { type: 'setup_recharge' }
    | {
          type: 'update_recharge'
          earn: ConfiguredEarn
          cardRecharge: CardRechargeEnabled
      }
    | {
          type: 'activate_physical_card'
          notActivatedPhysicalCards: NonEmptyArray<NotActivatedPhysicalCard>
      }

type Msg =
    | MsgOf<typeof CardViewPin>
    | MsgOf<typeof DisconnectCardPopup>
    | MsgOf<typeof ViewCardSafeAddress>
    | MsgOf<typeof SwitchCard>
    | MsgOf<typeof AddToWallet>
    | MsgOf<typeof ConfigureRecharge>
    | MsgOf<typeof ActivatePhysicalCard>

export const Modal = ({
    state,
    earn,
    cardBalance,
    cardConfig,
    networkRPCMap,
    portfolioMap,
    accountsMap,
    keyStoreMap,
    cardReadonlySigner,
    networkMap,
    gnosisPayAccountOnboardedState,
    encryptedPassword,
    sessionPassword,
    gasCurrencyPresetMap,
    feePresetMap,
    keyStore,
    installationId,
    earnTakerMetrics,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null

        case 'view_pin':
            return (
                <UIModal>
                    <CardViewPin
                        installationId={installationId}
                        gnosisPayAccountOnboardedState={
                            gnosisPayAccountOnboardedState
                        }
                        onMsg={onMsg}
                        encryptedPassword={encryptedPassword}
                        cardReadonlySigner={cardReadonlySigner}
                        sessionPassword={sessionPassword}
                        keyStore={keyStore}
                    />
                </UIModal>
            )
        case 'disconnect_card':
            return (
                <DisconnectCardPopup
                    cardReadonlySigner={cardReadonlySigner}
                    onMsg={onMsg}
                />
            )

        case 'view_card_safe_address':
            return (
                <UIModal>
                    <ViewCardSafeAddress
                        cardCurrency={cardConfig.currency}
                        cardSafeAddress={cardConfig.lastSeenSafeAddress}
                        installationId={installationId}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'switch_card':
            return (
                <UIModal>
                    <SwitchCard
                        sessionPassword={sessionPassword}
                        keyStore={keyStore}
                        cardReadonlySigner={cardReadonlySigner}
                        gnosisPayAccountOnboardedState={
                            gnosisPayAccountOnboardedState
                        }
                        installationId={installationId}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'wallet_pay_flow':
            return (
                <UIModal>
                    <AddToWallet
                        location="card_settings"
                        cardConfig={cardConfig}
                        gnosisPayAccountOnboardedState={
                            gnosisPayAccountOnboardedState
                        }
                        encryptedPassword={encryptedPassword}
                        platform={state.platform}
                        installationId={installationId}
                        sessionPassword={sessionPassword}
                        cardReadonlySigner={cardReadonlySigner}
                        keyStore={keyStore}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'setup_recharge':
        case 'update_recharge':
            return (
                <UIModal>
                    <ConfigureRecharge
                        earn={earn}
                        cardBalance={cardBalance}
                        accountsMap={accountsMap}
                        keyStoreMap={keyStoreMap}
                        portfolioMap={portfolioMap}
                        cardConfig={cardConfig}
                        networkRPCMap={networkRPCMap}
                        networkMap={networkMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        sessionPassword={sessionPassword}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        feePresetMap={feePresetMap}
                        keyStore={keyStore}
                        earnTakerMetrics={earnTakerMetrics}
                        location="card_screen"
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'activate_physical_card':
            return (
                <UIModal>
                    <ActivatePhysicalCard
                        cardConfig={cardConfig}
                        location="card_settings"
                        gnosisPayAccountState={gnosisPayAccountOnboardedState}
                        sessionPassword={sessionPassword}
                        keyStore={keyStore}
                        notActivatedPhysicalCards={
                            state.notActivatedPhysicalCards
                        }
                        cardReadonlySigner={cardReadonlySigner}
                        installationId={installationId}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        default:
            notReachable(state)
    }
}
