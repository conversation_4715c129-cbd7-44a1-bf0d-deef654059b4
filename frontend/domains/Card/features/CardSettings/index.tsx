import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardBalance,
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { DelayQueueState } from '@zeal/domains/Card/api/fetchDelayQueueState'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { Earn, EarnTakerMetrics } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    onMsg: (msg: Msg) => void
    cardReadonlySigner: Account
    cardBalance: CardBalance | null
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    earn: Earn
    earnTakerMetrics: EarnTakerMetrics
    keyStore: CardSlientSignKeyStore
    installationId: string
    notificationsConfig: NotificationsConfig
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    delayQueueStatePollable: PollableData<DelayQueueState, unknown>
    encryptedPassword: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    customCurrencyMap: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    keyStoreMap: KeyStoreMap
    defaultCurrencyConfig: DefaultCurrencyConfig
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_notifications_config_changed'
                  | 'on_card_spend_limit_changed'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_card_owner_clicked'
                  | 'add_wallet_clicked'
                  | 'on_add_card_owner_queued_successfully'
                  | 'on_remove_card_owner_queued_successfully'
                  | 'on_delay_queue_pollable_try_again_clicked'
                  | 'on_spend_limit_changed_successfully_close_clicked'
                  | 'on_new_virtual_card_created_successfully'
                  | 'on_new_physical_card_created_successfully'
                  | 'on_card_order_redirect_to_gnosis_pay_clicked'
                  | 'session_password_decrypted'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_card_disconnected'
                  | 'on_switch_card_new_card_selected'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_recharge_configured'
                  | 'on_physical_card_activated_info_screen_closed'
          }
      >

export const CardSettings = ({
    gnosisPayAccountOnboardedState,
    encryptedPassword,
    keyStore,
    installationId,
    notificationsConfig,
    cardReadonlySigner,
    cardConfig,
    earn,
    earnTakerMetrics,
    networkMap,
    networkRPCMap,
    accountsMap,
    gasCurrencyPresetMap,
    delayQueueStatePollable,
    portfolioMap,
    keyStoreMap,
    feePresetMap,
    currencyHiddenMap,
    customCurrencyMap,
    sessionPassword,
    cardBalance,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [state, setState] = useState<ModalState>({ type: 'closed' })

    return (
        <>
            <Layout
                defaultCurrencyConfig={defaultCurrencyConfig}
                accountsMap={accountsMap}
                cardConfig={cardConfig}
                earn={earn}
                keyStore={keyStore}
                delayQueueStatePollable={delayQueueStatePollable}
                gnosisPayAccountOnboardedState={gnosisPayAccountOnboardedState}
                cardReadonlySigner={cardReadonlySigner}
                currencyHiddenMap={currencyHiddenMap}
                notificationsConfig={notificationsConfig}
                installationId={installationId}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                portfolioMap={portfolioMap}
                sessionPassword={sessionPassword}
                keyStoreMap={keyStoreMap}
                encryptedPassword={encryptedPassword}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_notifications_config_changed':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_card_owner_clicked':
                        case 'add_wallet_clicked':
                        case 'on_add_card_owner_queued_successfully':
                        case 'on_remove_card_owner_queued_successfully':
                        case 'on_delay_queue_pollable_try_again_clicked':
                        case 'on_spend_limit_changed_successfully_close_clicked':
                        case 'on_new_virtual_card_created_successfully':
                        case 'on_new_physical_card_created_successfully':
                        case 'on_card_order_redirect_to_gnosis_pay_clicked':
                        case 'session_password_decrypted':
                            onMsg(msg)
                            break
                        case 'on_card_reveal_pin_clicked':
                            setState({ type: 'view_pin' })
                            break
                        case 'on_disconnect_card_clicked':
                            setState({ type: 'disconnect_card' })
                            break
                        case 'on_show_card_address_clicked':
                            setState({ type: 'view_card_safe_address' })
                            break
                        case 'on_switch_card_clicked':
                            setState({ type: 'switch_card' })
                            break
                        case 'on_connect_to_wallet_clicked':
                            setState({
                                type: 'wallet_pay_flow',
                                platform: msg.platform,
                            })
                            break
                        case 'on_disabled_recharge_list_item_clicked':
                            setState({
                                type: 'setup_recharge',
                            })
                            break
                        case 'on_enabled_recharge_list_item_clicked':
                            setState({
                                type: 'update_recharge',
                                cardRecharge: msg.cardRecharge,
                                earn: msg.earn,
                            })
                            break
                        case 'on_activate_physical_card_clicked':
                            setState({
                                type: 'activate_physical_card',
                                notActivatedPhysicalCards:
                                    msg.notActivatedPhysicalCards,
                            })
                            break

                        default:
                            notReachable(msg)
                    }
                }}
            />

            <Modal
                state={state}
                cardBalance={cardBalance}
                earn={earn}
                cardConfig={cardConfig}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                portfolioMap={portfolioMap}
                accountsMap={accountsMap}
                keyStoreMap={keyStoreMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                cardReadonlySigner={cardReadonlySigner}
                installationId={installationId}
                gnosisPayAccountOnboardedState={gnosisPayAccountOnboardedState}
                encryptedPassword={encryptedPassword}
                keyStore={keyStore}
                sessionPassword={sessionPassword}
                earnTakerMetrics={earnTakerMetrics}
                customCurrencyMap={customCurrencyMap}
                defaultCurrencyConfig={defaultCurrencyConfig}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'lock_screen_close_click':
                        case 'on_activate_physical_card_closed':
                            setState({ type: 'closed' })
                            break

                        case 'on_physical_card_activated_info_screen_closed':
                            onMsg(msg)
                            setState({ type: 'closed' })
                            break

                        case 'on_card_disconnected':
                        case 'on_switch_card_new_card_selected':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_recharge_configured':
                            onMsg(msg)
                            break

                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
