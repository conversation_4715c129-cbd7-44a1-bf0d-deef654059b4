import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardBalance,
    CardSlientSignKeyStore,
    CardTransaction,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { DelayQueueState } from '@zeal/domains/Card/api/fetchDelayQueueState'
import { CardAddCash } from '@zeal/domains/Card/features/CardAddCash'
import { CardSettings } from '@zeal/domains/Card/features/CardSettings'
import { CardTransactionActivity } from '@zeal/domains/Card/features/CardTransactionActivity'
import { CardWithdraw } from '@zeal/domains/Card/features/CardWithdraw'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    CardRechargeEnabled,
    ConfiguredEarn,
    Earn,
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
} from '@zeal/domains/Earn'
import { ConfigureRecharge } from '@zeal/domains/Earn/features/ConfigureRecharge'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { LockScreenPopup } from '@zeal/domains/Password/features/LockScreenPopup'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    state: State
    cardReadonlySigner: Account
    delayQueueStatePollable: PollableData<DelayQueueState, unknown>
    earn: Earn
    accountsMap: AccountsMap
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    encryptedPassword: string
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    isEthereumNetworkFeeWarningSeen: boolean
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    currencyPinMap: CurrencyPinMap
    networkMap: NetworkMap
    customCurrencyMap: CustomCurrencyMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    notificationsConfig: NotificationsConfig
    keyStore: CardSlientSignKeyStore
    earnTakerMetrics: EarnTakerMetrics
    cardBalance: CardBalance | null

    defaultCurrencyConfig: DefaultCurrencyConfig
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    onMsg: (msg: Msg) => void
}

export type State =
    | { type: 'closed' }
    | { type: 'card_settings' }
    | { type: 'lock_screen_popup' }
    | { type: 'add_cash' }
    | {
          type: 'update_recharge'
          cardRecharge: CardRechargeEnabled
          earn: ConfiguredEarn
      }
    | { type: 'setup_recharge' }
    | { type: 'card_withdraw' }
    | {
          type: 'card_activity'
          recentTransactions: NonEmptyArray<CardTransaction>
      }

type Msg =
    | { type: 'close' }
    | MsgOf<typeof LockScreenPopup>
    | MsgOf<typeof CardSettings>
    | MsgOf<typeof ConfigureRecharge>
    | MsgOf<typeof CardAddCash>
    | MsgOf<typeof CardWithdraw>
    | MsgOf<typeof CardTransactionActivity>

export const Modal = ({
    gnosisPayAccountOnboardedState,
    encryptedPassword,
    delayQueueStatePollable,
    state,
    earn,
    keyStore,
    accountsMap,
    installationId,
    gasCurrencyPresetMap,
    isEthereumNetworkFeeWarningSeen,
    customCurrencyMap,
    currencyHiddenMap,
    currencyPinMap,
    feePresetMap,
    networkMap,
    networkRPCMap,
    sessionPassword,
    portfolioMap,
    keyStoreMap,
    notificationsConfig,
    cardReadonlySigner,
    earnTakerMetrics,
    cardConfig,
    cardBalance,
    defaultCurrencyConfig,
    earnHistoricalTakerUserCurrencyRateMap,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'card_settings':
            return (
                <UIModal>
                    <CardSettings
                        cardConfig={cardConfig}
                        cardBalance={cardBalance}
                        earn={earn}
                        earnTakerMetrics={earnTakerMetrics}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        networkMap={networkMap}
                        delayQueueStatePollable={delayQueueStatePollable}
                        keyStore={keyStore}
                        networkRPCMap={networkRPCMap}
                        cardReadonlySigner={cardReadonlySigner}
                        notificationsConfig={notificationsConfig}
                        installationId={installationId}
                        accountsMap={accountsMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        portfolioMap={portfolioMap}
                        sessionPassword={sessionPassword}
                        currencyHiddenMap={currencyHiddenMap}
                        customCurrencyMap={customCurrencyMap}
                        keyStoreMap={keyStoreMap}
                        onMsg={onMsg}
                        gnosisPayAccountOnboardedState={
                            gnosisPayAccountOnboardedState
                        }
                        encryptedPassword={encryptedPassword}
                    />
                </UIModal>
            )
        case 'card_activity':
            return (
                <UIModal>
                    <CardTransactionActivity
                        earnHistoricalTakerUserCurrencyRateMap={
                            earnHistoricalTakerUserCurrencyRateMap
                        }
                        keyStore={keyStore}
                        sessionPassword={sessionPassword}
                        recentTransactions={state.recentTransactions}
                        networkMap={networkMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        cardConfig={cardConfig}
                        networkRpcMap={networkRPCMap}
                        earn={earn}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'add_cash':
            return (
                <UIModal>
                    <CardAddCash
                        customCurrencies={customCurrencyMap}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        initialSender={cardReadonlySigner}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        networkMap={networkMap}
                        accountsMap={accountsMap}
                        keyStoreMap={keyStoreMap}
                        portfolioMap={portfolioMap}
                        currencyHiddenMap={currencyHiddenMap}
                        sessionPassword={sessionPassword}
                        networkRPCMap={networkRPCMap}
                        currencyPinMap={currencyPinMap}
                        customCurrencyMap={customCurrencyMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        cardConfig={cardConfig}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'setup_recharge':
        case 'update_recharge':
            return (
                <UIModal>
                    <ConfigureRecharge
                        earn={earn}
                        cardBalance={cardBalance}
                        accountsMap={accountsMap}
                        keyStoreMap={keyStoreMap}
                        portfolioMap={portfolioMap}
                        cardConfig={cardConfig}
                        networkRPCMap={networkRPCMap}
                        networkMap={networkMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        sessionPassword={sessionPassword}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        feePresetMap={feePresetMap}
                        keyStore={keyStore}
                        onMsg={onMsg}
                        earnTakerMetrics={earnTakerMetrics}
                        location="card_screen"
                    />
                </UIModal>
            )
        case 'lock_screen_popup':
            return (
                <LockScreenPopup
                    installationId={installationId}
                    encryptedPassword={encryptedPassword}
                    onMsg={onMsg}
                />
            )
        case 'card_withdraw':
            return (
                <UIModal>
                    <CardWithdraw
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        cardReadonlySigner={cardReadonlySigner}
                        delayQueueStatePollable={delayQueueStatePollable}
                        gnosisPayAccountOnboardedState={
                            gnosisPayAccountOnboardedState
                        }
                        cardCashback={cardConfig.cashback}
                        accountsMap={accountsMap}
                        networkMap={networkMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        sessionPassword={sessionPassword}
                        installationId={installationId}
                        networkRPCMap={networkRPCMap}
                        keyStoreMap={keyStoreMap}
                        onMsg={onMsg}
                        currencyPinMap={currencyPinMap}
                        currencyHiddenMap={currencyHiddenMap}
                        portfolioMap={portfolioMap}
                        keyStore={keyStore}
                    />
                </UIModal>
            )

        default:
            notReachable(state)
    }
}
