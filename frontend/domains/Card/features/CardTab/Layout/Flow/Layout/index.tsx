import { useEffect } from 'react'

import { CardWidget } from '@zeal/uikit/CardWidget'
import { Column } from '@zeal/uikit/Column'
import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'
import { Screen } from '@zeal/uikit/Screen'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { withRetries } from '@zeal/toolkit/Function'
import { useReloadableData } from '@zeal/toolkit/LoadableData/ReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealMobilePlatform } from '@zeal/toolkit/OS/ZealPlatform'
import { useLiveRef, useUpdateEffect } from '@zeal/toolkit/React'
import { Address } from '@zeal/toolkit/Web3/address'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
    GnosisPayAccountState2,
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { fetchGnosisPayAccountState2WithSilentLogin } from '@zeal/domains/Card/api/fetchGnosisPayAccountState'
import { CardLoadingScreen } from '@zeal/domains/Card/components/CardLoadingScreen'
import { CardOwerNotFoundWallet } from '@zeal/domains/Card/features/CardOwerNotFoundWallet'
import { getOnboardedCardConfig } from '@zeal/domains/Card/helpers/getOnboardedCardConfig'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    Earn,
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
} from '@zeal/domains/Earn'
import { AppErrorBanner } from '@zeal/domains/Error/components/AppErrorBanner'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { NotOnboarded } from './NotOnboarded'
import { Onboarded } from './Onboarded'
import { OnboardedFork } from './OnboardedFork'

type Props = {
    cardReadonlySigner: Account
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    isEthereumNetworkFeeWarningSeen: boolean
    keyStore: CardSlientSignKeyStore
    installationId: string
    earnTakerMetrics: EarnTakerMetrics
    encryptedPassword: string
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    currencyPinMap: CurrencyPinMap
    networkMap: NetworkMap
    customCurrencyMap: CustomCurrencyMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    notificationsConfig: NotificationsConfig
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    earn: Earn

    defaultCurrencyConfig: DefaultCurrencyConfig
    installationCampaign: string | null
    refreshContainerState: RefreshContainerState
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_card_onboarded_account_state_received'
          cardReadonlySignerAddress: Address
          gnosisAccountState: GnosisPayAccountOnboardedState
      }
    | {
          type: 'on_virtual_card_order_created_animation_completed'

          platform: ZealMobilePlatform
          gnosisAccountState: GnosisPayAccountOnboardedState
      }
    | Extract<
          MsgOf<typeof NotOnboarded>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_transaction_item_clicked'
                  | 'on_activate_existing_monerium_account_click'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_switch_bank_transfer_provider_clicked'
                  | 'on_monerium_deposit_success_go_to_wallet_clicked'
                  | 'on_card_disconnected'
                  | 'on_do_bank_transfer_clicked'
                  | 'on_card_onboarded_account_state_received'
                  | 'on_order_new_card_clicked'
                  | 'on_order_import_card_clicked'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'on_physical_card_activated_info_screen_closed'
                  | 'on_card_order_redirect_to_gnosis_pay_clicked'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
          }
      >
    | Extract<
          MsgOf<typeof Onboarded>,
          {
              type:
                  | 'import_keys_button_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_predefined_fee_preset_selected'
                  | 'cancel_submitted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'import_card_owner_clicked'
                  | 'add_wallet_clicked'
                  | 'on_new_virtual_card_created_successfully'
                  | 'on_notifications_config_changed'
                  | 'on_bank_transfer_selected'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'track_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_accounts_create_success_animation_finished'
                  | 'hardware_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_top_up_transaction_complete_close'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_recharge_configured'
                  | 'on_card_disconnected'
                  | 'on_switch_card_new_card_selected'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_transaction_item_clicked'
                  | 'on_get_cashback_currency_clicked'
                  | 'on_card_transactions_fetch_success'
                  | 'on_card_freeze_toggle_failed'
                  | 'on_fallback_freeze_card_click'
                  | 'on_app_rating_submitted'
                  | 'on_cashback_celebration_triggered'
                  | 'on_dismiss_add_to_wallet_banner_clicked'
                  | 'on_card_onboarded_state_refresh_pulled'
                  | 'on_cashback_loaded'
                  | 'on_earn_last_recharge_transaction_hash_loaded'
                  | 'on_card_order_redirect_to_gnosis_pay_clicked'
                  | 'on_card_top_up_success'
                  | 'on_card_top_up_banner_dismissed'
                  | 'on_pending_card_top_up_state_changed'
          }
      >
    | MsgOf<typeof CardLoadingScreen>
    | Extract<
          MsgOf<typeof CardOwerNotFoundWallet>,
          {
              type:
                  | 'on_card_disconnected'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
          }
      >

const GNOSIS_PAY_ACCOUNT_FETCH_NUM_RETRIES = 2
const GNOSIS_PAY_ACCOUNT_FETCH_RETRY_DELAY_MS = 2000

const fetch = async ({
    sessionPassword,
    cardReadonlySigner,
    keyStore,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    selectedCardId,
    signal,
}: {
    selectedCardId: string | null
    cardReadonlySigner: Account
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<{
    gnosisAccountState: GnosisPayAccountState2
}> => {
    const gnosisAccountState = await fetchGnosisPayAccountState2WithSilentLogin(
        {
            sessionPassword,
            keyStore,
            readonlySignerAddress: cardReadonlySigner.address,
            networkMap,
            networkRPCMap,
            defaultCurrencyConfig,
            selectedCardId,
            signal,
        }
    )

    return { gnosisAccountState }
}

export const Layout = ({
    earn,
    cardReadonlySigner,
    appRating,
    celebrationConfig,
    accountsMap,
    keyStore,
    keyStoreMap,
    installationId,
    transactionActivitiesCacheMap,
    portfolioMap,
    currencyHiddenMap,
    encryptedPassword,
    sessionPassword,
    customCurrencyMap,
    currencyPinMap,
    gasCurrencyPresetMap,
    earnTakerMetrics,
    installationCampaign,
    networkRPCMap,
    networkMap,
    feePresetMap,
    cardConfig,
    notificationsConfig,
    isEthereumNetworkFeeWarningSeen,
    defaultCurrencyConfig,
    refreshContainerState,
    earnHistoricalTakerUserCurrencyRateMap,
    onMsg,
}: Props) => {
    const liveOnMsg = useLiveRef(onMsg)
    const liveReadonlySigner = useLiveRef(cardReadonlySigner)

    const selectedCardId = (() => {
        switch (cardConfig.type) {
            case 'card_readonly_signer_address_is_selected_fully_onboarded':
                return cardConfig.selectedCardId
            case 'card_readonly_signer_address_is_selected':
                return null

            /* istanbul ignore next */
            default:
                return notReachable(cardConfig)
        }
    })()

    const [loadable, setLoadable] = useReloadableData(
        withRetries({
            retries: GNOSIS_PAY_ACCOUNT_FETCH_NUM_RETRIES,
            delayMs: GNOSIS_PAY_ACCOUNT_FETCH_RETRY_DELAY_MS,
            fn: fetch,
        }),
        {
            type: 'loading',
            params: {
                sessionPassword,
                keyStore,
                cardReadonlySigner,
                selectedCardId,
                networkRPCMap,
                networkMap,
                defaultCurrencyConfig,
            },
        }
    )

    const liveNetworkRpcMap = useLiveRef(networkRPCMap)

    useUpdateEffect(() => {
        // We want to reload the account state every time the selected card changes
        setLoadable({
            type: 'loading',
            params: {
                sessionPassword,
                keyStore,
                cardReadonlySigner: liveReadonlySigner.current,
                selectedCardId,
                networkRPCMap: liveNetworkRpcMap.current,
                networkMap,
                defaultCurrencyConfig,
            },
        })
    }, [selectedCardId, liveNetworkRpcMap, liveReadonlySigner, setLoadable])

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
            case 'reloading':
                break
            case 'loaded':
                switch (loadable.data.gnosisAccountState.type) {
                    case 'not_onboarded':
                        break
                    case 'onboarded':
                        if (!loadable.data.gnosisAccountState.fullName) {
                            captureError(
                                new ImperativeError(
                                    'Missing full name in GnosisPayAccountOnboardedState'
                                )
                            )
                        }

                        liveOnMsg.current({
                            type: 'on_card_onboarded_account_state_received',
                            cardReadonlySignerAddress:
                                liveReadonlySigner.current.address,
                            gnosisAccountState:
                                loadable.data.gnosisAccountState,
                        })
                        break
                    /* istanbul ignore next */
                    default:
                        return notReachable(loadable.data.gnosisAccountState)
                }
                break
            case 'subsequent_failed':
                captureError(loadable.error)
                break

            case 'error':
                // handled by widget
                break

            default:
                notReachable(loadable)
        }
    }, [liveReadonlySigner, liveOnMsg, loadable])

    useEffect(() => {
        // TODO @resetko-zeal low prio last thing to do we need to reload loadable once we back from background (tab activation on web / app activation on mobile)
    }, [])

    switch (loadable.type) {
        case 'loading':
            return (
                <CardLoadingScreen
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    cardConfig={cardConfig}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )

        case 'error':
            const error = parseAppError(loadable.error)

            switch (error.type) {
                case 'gnosis_pay_user_is_not_signed_up':
                    return (
                        <CardOwerNotFoundWallet
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            variant="not_closable"
                            installationId={installationId}
                            installationCampaign={installationCampaign}
                            cardReadonlySignerAddress={
                                cardConfig.readonlySignerAddress
                            }
                            accountsMap={accountsMap}
                            keystoreMap={keyStoreMap}
                            portfolioMap={portfolioMap}
                            currencyHiddenMap={currencyHiddenMap}
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            sessionPassword={sessionPassword}
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'close':
                                        throw new ImperativeError(
                                            'Got impossible msg in CardOwerNotFoundWallet with type "not_closable"',
                                            { msg: msg.type }
                                        )
                                        break
                                    case 'on_card_disconnected':
                                    case 'on_card_import_on_import_keys_clicked':
                                    case 'on_create_smart_wallet_clicked':
                                    case 'on_card_imported_success_animation_complete':
                                    case 'on_onboarded_card_imported_success_animation_complete':
                                    case 'on_usd_taker_metrics_loaded':
                                    case 'on_eur_taker_metrics_loaded':
                                        onMsg(msg)
                                        break

                                    /* istanbul ignore next */
                                    default:
                                        notReachable(msg)
                                }
                            }}
                        />
                    )

                default:
                    return (
                        <Screen
                            padding="controller_tabs_fullscreen"
                            background="light"
                            onNavigateBack={null}
                        >
                            <Column spacing={8}>
                                <CardWidget
                                    cardType="physical"
                                    side="front"
                                    variant="active"
                                    settingsIcon={null}
                                    balancePending={null}
                                    balanceSpendable={null}
                                    shortCardNumber={null}
                                    rechargeTag={null}
                                />

                                <AppErrorBanner
                                    installationId={installationId}
                                    error={error}
                                    onMsg={(msg) => {
                                        switch (msg.type) {
                                            case 'try_again_clicked':
                                                setLoadable({
                                                    type: 'loading',
                                                    params: loadable.params,
                                                })

                                                break
                                            /* istanbul ignore next */
                                            default:
                                                return notReachable(msg.type)
                                        }
                                    }}
                                />
                            </Column>
                        </Screen>
                    )
            }

        case 'reloading':
        case 'subsequent_failed':
        case 'loaded': {
            switch (loadable.data.gnosisAccountState.type) {
                case 'not_onboarded':
                    switch (cardConfig.type) {
                        case 'card_readonly_signer_address_is_selected':
                            return (
                                <NotOnboarded
                                    installationCampaign={installationCampaign}
                                    cardConfig={cardConfig}
                                    currencyHiddenMap={currencyHiddenMap}
                                    defaultCurrencyConfig={
                                        defaultCurrencyConfig
                                    }
                                    portfolioMap={portfolioMap}
                                    installationId={installationId}
                                    keyStoreMap={keyStoreMap}
                                    accountsMap={accountsMap}
                                    initialGnosisPayAccountState={
                                        loadable.data.gnosisAccountState
                                    }
                                    feePresetMap={feePresetMap}
                                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                                    networkMap={networkMap}
                                    networkRPCMap={networkRPCMap}
                                    sessionPassword={sessionPassword}
                                    keyStore={keyStore}
                                    cardReadOnlySigner={cardReadonlySigner}
                                    earnTakerMetrics={earnTakerMetrics}
                                    onMsg={(msg) => {
                                        switch (msg.type) {
                                            case 'on_4337_auto_gas_token_selection_clicked':
                                            case 'on_4337_gas_currency_selected':
                                            case 'on_transaction_item_clicked':
                                            case 'on_activate_existing_monerium_account_click':
                                            case 'on_create_smart_wallet_clicked':
                                            case 'on_switch_bank_transfer_provider_clicked':
                                            case 'on_monerium_deposit_success_go_to_wallet_clicked':
                                            case 'on_card_disconnected':
                                            case 'on_do_bank_transfer_clicked':
                                            case 'on_card_onboarded_account_state_received':
                                            case 'on_order_new_card_clicked':
                                            case 'on_order_import_card_clicked':
                                            case 'on_card_import_on_import_keys_clicked':
                                            case 'on_card_imported_success_animation_complete':
                                            case 'on_physical_card_activated_info_screen_closed':
                                            case 'on_onboarded_card_imported_success_animation_complete':
                                            case 'on_card_order_redirect_to_gnosis_pay_clicked':
                                            case 'on_usd_taker_metrics_loaded':
                                            case 'on_eur_taker_metrics_loaded':
                                                onMsg(msg)
                                                break

                                            case 'on_virtual_card_order_created_animation_completed':
                                                switch (
                                                    msg.gnosisPayStateResult
                                                        .type
                                                ) {
                                                    case 'Failure':
                                                        {
                                                            switch (
                                                                msg
                                                                    .gnosisPayStateResult
                                                                    .reason.type
                                                            ) {
                                                                case 'failed_to_fetch':
                                                                    setLoadable(
                                                                        {
                                                                            type: 'loading',
                                                                            params: loadable.params,
                                                                        }
                                                                    )
                                                                    break
                                                                case 'wallet_not_installed':
                                                                case 'platform_not_supported_wallet_connect':
                                                                case 'country_not_support_wallet':
                                                                    setLoadable(
                                                                        {
                                                                            type: 'loaded',
                                                                            params: loadable.params,
                                                                            data: {
                                                                                gnosisAccountState:
                                                                                    msg
                                                                                        .gnosisPayStateResult
                                                                                        .reason
                                                                                        .gnosisState,
                                                                            },
                                                                        }
                                                                    )
                                                                    onMsg({
                                                                        type: 'on_card_onboarded_account_state_received',
                                                                        cardReadonlySignerAddress:
                                                                            cardReadonlySigner.address,
                                                                        gnosisAccountState:
                                                                            msg
                                                                                .gnosisPayStateResult
                                                                                .reason
                                                                                .gnosisState,
                                                                    })
                                                                    break

                                                                /* istanbul ignore next */
                                                                default:
                                                                    notReachable(
                                                                        msg
                                                                            .gnosisPayStateResult
                                                                            .reason
                                                                    )
                                                            }
                                                        }

                                                        break
                                                    case 'Success':
                                                        setLoadable({
                                                            type: 'loaded',
                                                            params: loadable.params,
                                                            data: {
                                                                gnosisAccountState:
                                                                    msg
                                                                        .gnosisPayStateResult
                                                                        .data
                                                                        .gnosisState,
                                                            },
                                                        })
                                                        onMsg({
                                                            type: 'on_virtual_card_order_created_animation_completed',
                                                            gnosisAccountState:
                                                                msg
                                                                    .gnosisPayStateResult
                                                                    .data
                                                                    .gnosisState,
                                                            platform:
                                                                msg
                                                                    .gnosisPayStateResult
                                                                    .data
                                                                    .platform,
                                                        })
                                                        break

                                                    default:
                                                        notReachable(
                                                            msg.gnosisPayStateResult
                                                        )
                                                }

                                                break

                                            /* istanbul ignore next */
                                            default:
                                                notReachable(msg)
                                        }
                                    }}
                                />
                            )
                        case 'card_readonly_signer_address_is_selected_fully_onboarded':
                            return (
                                <CardLoadingScreen
                                    defaultCurrencyConfig={
                                        defaultCurrencyConfig
                                    }
                                    cardConfig={cardConfig}
                                    installationId={installationId}
                                    onMsg={onMsg}
                                />
                            )
                        /* istanbul ignore next */
                        default:
                            return notReachable(cardConfig)
                    }

                case 'onboarded':
                    const gnosisPayAccountOnboardedState =
                        loadable.data.gnosisAccountState

                    return (
                        <OnboardedFork
                            transactionActivitiesCacheMap={
                                transactionActivitiesCacheMap
                            }
                            earnHistoricalTakerUserCurrencyRateMap={
                                earnHistoricalTakerUserCurrencyRateMap
                            }
                            appRating={appRating}
                            celebrationConfig={celebrationConfig}
                            refreshContainerState={refreshContainerState}
                            earnTakerMetrics={earnTakerMetrics}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            isEthereumNetworkFeeWarningSeen={
                                isEthereumNetworkFeeWarningSeen
                            }
                            earn={earn}
                            keyStore={keyStore}
                            cardReadonlySigner={cardReadonlySigner}
                            notificationsConfig={notificationsConfig}
                            cardConfig={getOnboardedCardConfig(
                                cardConfig,
                                gnosisPayAccountOnboardedState
                            )}
                            networkMap={networkMap}
                            keyStoreMap={keyStoreMap}
                            portfolioMap={portfolioMap}
                            currencyHiddenMap={currencyHiddenMap}
                            sessionPassword={sessionPassword}
                            networkRPCMap={networkRPCMap}
                            currencyPinMap={currencyPinMap}
                            customCurrencyMap={customCurrencyMap}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            accountsMap={accountsMap}
                            installationId={installationId}
                            encryptedPassword={encryptedPassword}
                            gnosisPayAccountOnboardedState={
                                gnosisPayAccountOnboardedState
                            }
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'import_keys_button_clicked':
                                    case 'on_4337_auto_gas_token_selection_clicked':
                                    case 'on_4337_gas_currency_selected':
                                    case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                    case 'on_predefined_fee_preset_selected':
                                    case 'cancel_submitted':
                                    case 'on_transaction_completed_splash_animation_screen_competed':
                                    case 'transaction_request_replaced':
                                    case 'transaction_submited':
                                    case 'import_card_owner_clicked':
                                    case 'add_wallet_clicked':
                                    case 'on_new_virtual_card_created_successfully':
                                    case 'on_notifications_config_changed':
                                    case 'on_bank_transfer_selected':
                                    case 'on_address_scanned':
                                    case 'on_address_scanned_and_add_label':
                                    case 'on_ethereum_network_fee_warning_understand_clicked':
                                    case 'track_wallet_clicked':
                                    case 'on_account_create_request':
                                    case 'on_accounts_create_success_animation_finished':
                                    case 'hardware_wallet_clicked':
                                    case 'safe_wallet_clicked':
                                    case 'recover_safe_wallet_clicked':
                                    case 'on_add_label_to_track_only_account_during_send':
                                    case 'on_top_up_transaction_complete_close':
                                    case 'on_swaps_io_swap_request_created':
                                    case 'on_usd_taker_metrics_loaded':
                                    case 'on_eur_taker_metrics_loaded':
                                    case 'on_recharge_configured':
                                    case 'on_card_disconnected':
                                    case 'on_switch_card_new_card_selected':
                                    case 'on_select_rpc_click':
                                    case 'on_rpc_change_confirmed':
                                    case 'on_transaction_item_clicked':
                                    case 'on_get_cashback_currency_clicked':
                                    case 'on_card_transactions_fetch_success':
                                    case 'on_card_freeze_toggle_failed':
                                    case 'on_fallback_freeze_card_click':
                                    case 'on_app_rating_submitted':
                                    case 'on_cashback_celebration_triggered':
                                    case 'on_dismiss_add_to_wallet_banner_clicked':
                                    case 'on_card_onboarded_state_refresh_pulled':
                                    case 'on_cashback_loaded':
                                    case 'on_earn_last_recharge_transaction_hash_loaded':
                                    case 'on_card_order_redirect_to_gnosis_pay_clicked':
                                    case 'on_card_top_up_success':
                                    case 'on_card_top_up_banner_dismissed':
                                    case 'on_pending_card_top_up_state_changed':
                                        onMsg(msg)
                                        break

                                    case 'on_physical_card_activated_info_screen_closed':
                                        onMsg(msg)
                                        setLoadable({
                                            type: 'reloading',
                                            params: loadable.params,
                                            data: loadable.data,
                                        })
                                        break

                                    /* istanbul ignore next */
                                    default:
                                        notReachable(msg)
                                }
                            }}
                        />
                    )

                default:
                    return notReachable(loadable.data.gnosisAccountState)
            }
        }

        default:
            return notReachable(loadable)
    }
}
