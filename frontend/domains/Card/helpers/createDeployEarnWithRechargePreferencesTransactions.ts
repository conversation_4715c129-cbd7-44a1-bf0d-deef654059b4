import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { isNonEmptyArray } from '@zeal/toolkit/NonEmptyArray'

import { DeployedTaker, Earn } from '@zeal/domains/Earn'
import { createCoordinatorDeployTransaction } from '@zeal/domains/Earn/helpers/createCoordinatorDeployTransaction'
import { createUpdateRechargeRequest } from '@zeal/domains/Earn/helpers/createUpdateRechargeRequest'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'

import {
    ReadonlySignerSelectedOnboardedCardConfig,
    RechargePreferences,
} from '..'

export const createDeployEarnWithRechargePreferencesTransactions = ({
    earn,
    rechargePreferences,
    cardConfig,
}: {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    rechargePreferences: RechargePreferences
    earn: Earn
}): {
    configureEarnTransaction: EthSendTransaction | null
    configureRechargeTransaction: EthSendTransaction
} => {
    const configureEarnTrx = (() => {
        switch (earn.type) {
            case 'not_configured':
                return createCoordinatorDeployTransaction({
                    owner: cardConfig.readonlySignerAddress,
                    takerType: rechargePreferences.taker,
                    fromAddress: cardConfig.readonlySignerAddress,
                })
            case 'configured':
                return null

            /* istanbul ignore next */
            default:
                return notReachable(earn)
        }
    })()

    const rebalancers = earn.takers
        .filter((taker) => taker.type === rechargePreferences.taker)
        .map<DeployedTaker>((taker) => ({
            type: taker.type,
            address: taker.address,
            cryptoCurrency: taker.cryptoCurrency,
            // as we bundle earn configure transaction with enable recharge transaction,
            // we assume that taker is deployed at the time when enable recharge transaction is executed
            state: 'deployed',
        }))

    if (!isNonEmptyArray(rebalancers)) {
        throw new ImperativeError(
            'Cannot find correct taker specified in recharge preferences',
            {
                takerType: rechargePreferences.taker,
                takers: earn.takers,
            }
        )
    }

    const enableRecharegeTrx = createUpdateRechargeRequest({
        cardConfig,
        rebalancers,
        earnHolderAddress: earn.holder,
        threshold: rechargePreferences.threshold,
    })

    return {
        configureEarnTransaction: configureEarnTrx,
        configureRechargeTransaction: enableRecharegeTrx,
    }
}
