import { failure, Result, success } from '@zeal/toolkit/Result'

import { Country } from '@zeal/domains/Country'
import { COUNTRIES_MAP, TIMEZONES } from '@zeal/domains/Country/constants'

export const tryToGetUserTimeZoneCountries = (): Result<unknown, Country[]> => {
    console.log('mock BR')
    return success([COUNTRIES_MAP['BR']])
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone

    if (!timezone) {
        return failure('no_timezone')
    }
    const config = TIMEZONES[timezone]
    if (!config) {
        return failure('no config for timezone')
    }
    return success(config.codes.map((code) => COUNTRIES_MAP[code]))
}
