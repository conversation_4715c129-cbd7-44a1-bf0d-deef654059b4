import { useRef, useState } from 'react'
import { FormattedMessage } from 'react-intl'
import { TextInput } from 'react-native'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { Input } from '@zeal/uikit/Input'
import { DateInput } from '@zeal/uikit/Input/DateInput'
import { Screen } from '@zeal/uikit/Screen'
import { Spacer } from '@zeal/uikit/Spacer'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { differenceInYears, parseDate } from '@zeal/toolkit/Date'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import {
    failure,
    nonEmptyString,
    Result,
    shape,
    success,
} from '@zeal/toolkit/Result'

import { PersonalDetails } from '@zeal/domains/Currency/domains/BankTransfer/api/submitUnblockKycApplication'

export type InitialPersonalDetails = {
    firstName: string | null
    lastName: string | null
    dateOfBirth: string | null
}

type FormErrors = {
    dateOfBirth?:
        | { type: 'dob_required' }
        | { type: 'invalid_format' }
        | { type: 'must_be_past_date' }
        | { type: 'too_young' }
        | { type: 'too_old_to_be_true' }
    submit?:
        | { type: 'first_name_required' }
        | { type: 'last_name_required' }
        | { type: 'dob_required' }
        | { type: 'invalid_format' }
        | { type: 'must_be_past_date' }
        | { type: 'too_young' }
        | { type: 'too_old_to_be_true' }
}

type Msg =
    | { type: 'on_form_submitted'; completedForm: PersonalDetails }
    | {
          type: 'close'
      }

type Props = {
    initialPersonalDetails: InitialPersonalDetails
    onMsg: (msg: Msg) => void
}

const validateAsUserTypes = (
    form: InitialPersonalDetails
): Result<FormErrors, unknown> => {
    return shape({
        firstName: nonEmptyString(form.firstName).mapError(() => ({
            type: 'first_name_required' as const,
        })),
        lastName: nonEmptyString(form.lastName).mapError(() => ({
            type: 'last_name_required' as const,
        })),
        dateOfBirth: nonEmptyString(form.dateOfBirth).mapError(() => ({
            type: 'dob_required' as const,
        })),
    }).mapError(({ firstName, lastName, dateOfBirth }) => {
        return {
            submit: firstName || lastName || dateOfBirth,
        }
    })
}

const DATE_REG_EXP = /^\d\d\d\d-\d\d-\d\d$/
const MINIMUM_AGE = 18

const validateDate = (
    input: string
): Result<
    | { type: 'invalid_format' }
    | { type: 'must_be_past_date' }
    | { type: 'too_young' }
    | { type: 'too_old_to_be_true' },
    string
> => {
    if (!input.match(DATE_REG_EXP)) {
        return failure({ type: 'invalid_format' as const })
    }

    return parseDate(input)
        .mapError(() => ({
            type: 'invalid_format' as const,
        }))
        .andThen((date) => {
            if (date.valueOf() >= Date.now()) {
                return failure({ type: 'must_be_past_date' as const })
            }
            return success(date)
        })
        .andThen((date) =>
            differenceInYears(new Date(), date) < MINIMUM_AGE
                ? failure({ type: 'too_young' as const })
                : success(date)
        )
        .andThen((date) => {
            const tooOldToBeTrue = new Date().getFullYear() - 150
            if (date.getFullYear() < tooOldToBeTrue) {
                return failure({ type: 'too_old_to_be_true' as const })
            }
            return success(date)
        })
        .map(() => input)
}

const validateOnSubmit = (
    form: InitialPersonalDetails
): Result<FormErrors, PersonalDetails> => {
    return shape({
        firstName: nonEmptyString(form.firstName).mapError(() => ({
            type: 'first_name_required' as const,
        })),
        lastName: nonEmptyString(form.lastName).mapError(() => ({
            type: 'last_name_required' as const,
        })),
        dateOfBirth: nonEmptyString(form.dateOfBirth)
            .mapError(() => ({
                type: 'dob_required' as const,
            }))
            .andThen((date) => validateDate(date)),
    }).mapError(({ firstName, lastName, dateOfBirth }) => {
        return {
            dateOfBirth,
            submit: firstName || lastName || dateOfBirth,
        }
    })
}

export const PersonalDetailsForm = ({
    initialPersonalDetails,
    onMsg,
}: Props) => {
    const [isSubmitted, setIsSubmitted] = useState<boolean>(false)
    const [form, setForm] = useState<InitialPersonalDetails>(
        initialPersonalDetails
    )

    const lastNameInput = useRef<TextInput>(null)
    const dateOfBirthInput = useRef<TextInput>(null)

    const errors = isSubmitted
        ? validateOnSubmit(form).getFailureReason() || {}
        : validateAsUserTypes(form).getFailureReason() || {}

    const onSubmit = () => {
        setIsSubmitted(true)

        const validation = validateOnSubmit(form)

        switch (validation.type) {
            case 'Failure':
                break

            case 'Success': {
                onMsg({
                    type: 'on_form_submitted',
                    completedForm: validation.data,
                })
                break
            }

            default:
                notReachable(validation)
        }
    }

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
            />

            <Column spacing={24}>
                <Header
                    title={
                        <FormattedMessage
                            id="bank_transfer.personal_details.title"
                            defaultMessage="Your details"
                        />
                    }
                />

                <Column spacing={8}>
                    <Column spacing={8}>
                        <Text
                            variant="paragraph"
                            weight="regular"
                            color="textSecondary"
                        >
                            <FormattedMessage
                                id="bank_transfer.personal_details.first_name"
                                defaultMessage="First name"
                            />
                        </Text>

                        <Input
                            keyboardType="default"
                            returnKeyType="next"
                            autoComplete="name"
                            blurOnSubmit={false} // prevent keyboard flashing when pressing "next"
                            onSubmitEditing={() => {
                                switch (ZealPlatform.OS) {
                                    case 'ios':
                                    case 'android':
                                        lastNameInput.current?.focus()
                                        break
                                    case 'web':
                                        onSubmit()
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(ZealPlatform)
                                }
                            }}
                            onChange={(e) =>
                                setForm({
                                    ...form,
                                    firstName: e.nativeEvent.text,
                                })
                            }
                            state="normal"
                            placeholder="Thomas"
                            variant="regular"
                            value={form.firstName ?? ''}
                        />
                    </Column>
                    <Column spacing={8}>
                        <Text
                            variant="paragraph"
                            weight="regular"
                            color="textSecondary"
                        >
                            <FormattedMessage
                                id="bank_transfer.personal_details.last_name"
                                defaultMessage="Last name"
                            />
                        </Text>

                        <Input
                            ref={lastNameInput}
                            keyboardType="default"
                            autoComplete="name"
                            returnKeyType="next"
                            blurOnSubmit={false} // prevent keyboard flashing when pressing "next"
                            onSubmitEditing={() => {
                                switch (ZealPlatform.OS) {
                                    case 'ios':
                                    case 'android':
                                        dateOfBirthInput.current?.focus()
                                        break
                                    case 'web':
                                        onSubmit()
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(ZealPlatform)
                                }
                            }}
                            onChange={(e) =>
                                setForm({
                                    ...form,
                                    lastName: e.nativeEvent.text,
                                })
                            }
                            state="normal"
                            placeholder="Anderson"
                            variant="regular"
                            value={form.lastName ?? ''}
                        />
                    </Column>
                    <Column spacing={8}>
                        <Text
                            variant="paragraph"
                            weight="regular"
                            color="textSecondary"
                        >
                            <FormattedMessage
                                id="bank_transfer.personal_details.date_of_birth"
                                defaultMessage="Date of birth"
                            />
                        </Text>

                        <DateInput
                            value={form.dateOfBirth}
                            onChange={(value) => {
                                setForm({
                                    ...form,
                                    dateOfBirth: value,
                                })
                            }}
                        >
                            {({ value, onChange }) => (
                                <Input
                                    ref={dateOfBirthInput}
                                    keyboardType="number-pad"
                                    onSubmitEditing={onSubmit}
                                    onChange={onChange}
                                    placeholder="YYYY-MM-DD"
                                    variant="regular"
                                    state={
                                        !!errors?.dateOfBirth
                                            ? 'error'
                                            : 'normal'
                                    }
                                    message={
                                        errors?.dateOfBirth && (
                                            <DateMessage
                                                error={errors.dateOfBirth}
                                            />
                                        )
                                    }
                                    value={value}
                                />
                            )}
                        </DateInput>
                    </Column>
                </Column>
            </Column>

            <Spacer />

            <Actions variant="default">
                <Button
                    variant="primary"
                    size="regular"
                    disabled={!!errors.submit}
                    onClick={onSubmit}
                >
                    <FormattedMessage
                        id="action.continue"
                        defaultMessage="Continue"
                    />
                </Button>
            </Actions>
        </Screen>
    )
}

const DateMessage = ({
    error,
}: {
    error: NonNullable<FormErrors['dateOfBirth']>
}) => {
    switch (error.type) {
        case 'dob_required':
            return null

        case 'too_young':
            return (
                <FormattedMessage
                    id="bank_transfer.personal_details.date_of_birth.too_young"
                    defaultMessage="You must be at least 18 years old"
                />
            )

        case 'invalid_format':
        case 'must_be_past_date':
        case 'too_old_to_be_true':
            return (
                <FormattedMessage
                    id="bank_transfer.personal_details.date_of_birth.invalid_format"
                    defaultMessage="Date is invalid"
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(error)
    }
}
