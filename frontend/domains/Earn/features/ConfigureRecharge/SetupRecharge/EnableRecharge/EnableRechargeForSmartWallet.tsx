import { useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'
import { SuccessLayout } from '@zeal/uikit/SuccessLayout'

import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'

import {
    ReadonlySignerSelectedOnboardedCardConfig,
    RechargePreferences,
} from '@zeal/domains/Card'
import { createDeployEarnWithRechargePreferencesTransactions } from '@zeal/domains/Card/helpers/createDeployEarnWithRechargePreferencesTransactions'
import { ConfiguredEarn, <PERSON>arn } from '@zeal/domains/Earn'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { submitAndMonitorSponsored } from '@zeal/domains/UserOperation/api/submitAndMonitorSponsored'

import { getConfiguredEarnWithEnabledRecharge } from '../helpers/getConfiguredEarnWithEnabledRecharge'

type Msg =
    | { type: 'close' }
    | {
          type: 'on_recharge_setup_successfully'
          earn: ConfiguredEarn
      }

type Props = {
    earn: Earn
    networkMap: NetworkMap
    keyStore: Safe4337
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    installationId: string
    rechargePreferences: RechargePreferences
    onMsg: (msg: Msg) => void
}

export const EnableRechargeForSmartWallet = ({
    earn,
    networkMap,
    keyStore,
    networkRPCMap,
    sessionPassword,
    installationId,
    rechargePreferences,
    cardConfig,
    onMsg,
}: Props) => {
    const [{ configureRechargeTransaction, configureEarnTransaction }] =
        useState(
            createDeployEarnWithRechargePreferencesTransactions({
                cardConfig,
                earn,
                rechargePreferences,
            })
        )

    const [loadable, setLoadable] = useLoadableData(submitAndMonitorSponsored, {
        type: 'loading',
        params: {
            sessionPassword,
            installationId,
            keyStore,
            network: EARN_NETWORK,
            networkMap,
            networkRPCMap,
            rpcRequestsToBundle: [
                configureEarnTransaction,
                configureRechargeTransaction,
            ].filter(excludeNullValues),
            actionSource: {
                type: 'internal',
                transactionEventSource: 'earnDeployWithRechargePreferences',
            } as const,
        },
    })

    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    title={
                        <FormattedMessage
                            id="enable-recharge-for-smart-wallet.enabling-recharge.title"
                            defaultMessage="Enabling recharge"
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )

        case 'error':
            return (
                <>
                    <LoadingLayout
                        actionBar={
                            <ActionBar
                                left={
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => onMsg({ type: 'close' })}
                                    >
                                        {({ color }) => (
                                            <BackIcon size={24} color={color} />
                                        )}
                                    </IconButton>
                                }
                            />
                        }
                        title={
                            <FormattedMessage
                                id="enable-recharge-for-smart-wallet.enabling-recharge.title"
                                defaultMessage="Enabling recharge"
                            />
                        }
                        onClose={() => onMsg({ type: 'close' })}
                    />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        case 'loaded':
            return (
                <SuccessLayout
                    title={
                        <FormattedMessage
                            id="enable-recharge-for-smart-wallet.recharge-enabled.title"
                            defaultMessage="Recharge enabled"
                        />
                    }
                    onAnimationComplete={() => {
                        onMsg({
                            type: 'on_recharge_setup_successfully',
                            earn: getConfiguredEarnWithEnabledRecharge({
                                earn,
                                rechargePreferences,
                                cardSafeAddress: cardConfig.lastSeenSafeAddress,
                                cardCurrency: cardConfig.currency,
                            }),
                        })
                    }}
                />
            )
        default:
            return notReachable(loadable)
    }
}
