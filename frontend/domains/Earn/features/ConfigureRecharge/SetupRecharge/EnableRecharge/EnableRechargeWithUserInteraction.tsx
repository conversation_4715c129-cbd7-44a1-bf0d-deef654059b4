import { useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import {
    ReadonlySignerSelectedOnboardedCardConfig,
    RechargePreferences,
} from '@zeal/domains/Card'
import { createDeployEarnWithRechargePreferencesTransactions } from '@zeal/domains/Card/helpers/createDeployEarnWithRechargePreferencesTransactions'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { ConfiguredEarn, Earn } from '@zeal/domains/Earn'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { getTakerByTakerType } from '@zeal/domains/Earn/helpers/getTakerByTakerType'
import { KeyStoreMap, PrivateKey, SecretPhrase } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { getConfiguredEarnWithEnabledRecharge } from '../helpers/getConfiguredEarnWithEnabledRecharge'

type Msg =
    | { type: 'close' }
    | {
          type: 'on_recharge_setup_successfully'
          earn: ConfiguredEarn
      }
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type: 'on_predefined_fee_preset_selected'
          }
      >

type Props = {
    earn: Earn
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    rechargePreferences: RechargePreferences
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig

    accounts: AccountsMap
    keyStore: PrivateKey | SecretPhrase
    keystores: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string

    defaultCurrencyConfig: DefaultCurrencyConfig

    onMsg: (msg: Msg) => void
}

type State =
    | {
          type: 'configure_earn'
          transaction: EthSendTransaction
          configureRechargeTransaction: EthSendTransaction
      }
    | {
          type: 'configure_recharge'
          transaction: EthSendTransaction
          configuredEarn: ConfiguredEarn
      }

const calculateInitialState = ({
    configureEarnTransaction,
    configureRechargeTransaction,
    earn,
    rechargePreferences,
    cardConfig,
}: {
    configureEarnTransaction: EthSendTransaction | null
    configureRechargeTransaction: EthSendTransaction
    earn: Earn
    rechargePreferences: RechargePreferences
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
}): State => {
    if (configureEarnTransaction) {
        return {
            type: 'configure_earn',
            transaction: configureEarnTransaction,
            configureRechargeTransaction,
        }
    }

    return {
        type: 'configure_recharge',
        transaction: configureRechargeTransaction,
        configuredEarn: getConfiguredEarnWithEnabledRecharge({
            earn,
            rechargePreferences,
            cardSafeAddress: cardConfig.lastSeenSafeAddress,
            cardCurrency: cardConfig.currency,
        }),
    }
}

export const EnableRechargeWithUserInteraction = ({
    earn,
    networkRPCMap,
    sessionPassword,
    rechargePreferences,
    cardConfig,
    accounts,
    keyStore,
    keystores,
    networkMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [{ configureRechargeTransaction, configureEarnTransaction }] =
        useState(
            createDeployEarnWithRechargePreferencesTransactions({
                cardConfig,
                earn,
                rechargePreferences,
            })
        )

    const [state, setState] = useState<State>(
        calculateInitialState({
            configureEarnTransaction,
            configureRechargeTransaction,
            earn,
            rechargePreferences,
            cardConfig,
        })
    )

    const account = accounts[cardConfig.readonlySignerAddress]
    const network = EARN_NETWORK

    switch (state.type) {
        case 'configure_earn':
            return (
                <SendTransaction
                    sendTransactionRequests={[state.transaction]}
                    sessionPassword={sessionPassword}
                    account={account}
                    network={network}
                    networkRPCMap={networkRPCMap}
                    accounts={accounts}
                    keystores={keystores}
                    networkMap={networkMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    fetchSimulationByRequest={async () => {
                        return {
                            type: 'simulated',
                            simulation: {
                                transaction: {
                                    type: 'deploy_earn_account',
                                    owner: account,
                                    taker: getTakerByTakerType({
                                        takerType: rechargePreferences.taker,
                                        earn,
                                    }),
                                    takerApyMap: earn.takerApyMap,
                                },
                                currencies: {},
                                checks: [],
                            },
                        }
                    }}
                    fetchTransactionResultByRequest={async () => ({
                        transaction: {
                            type: 'deploy_earn_account',
                            owner: account,
                            taker: getTakerByTakerType({
                                takerType: rechargePreferences.taker,
                                earn,
                            }),
                            takerApyMap: earn.takerApyMap,
                        },
                        currencies: {},
                    })}
                    state={{ type: 'maximised' }}
                    actionSource={{
                        type: 'internal',
                        transactionEventSource: 'earnHolderDeploy',
                    }}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    portfolio={null}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'cancel_submitted':
                            case 'transaction_request_replaced':
                            case 'transaction_submited':
                                noop()
                                break
                            case 'on_minimize_click':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                                onMsg({
                                    type: 'close',
                                })
                                break
                            case 'on_predefined_fee_preset_selected':
                                onMsg(msg)
                                break
                            case 'on_close_transaction_status_not_found_modal':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_completed_transaction_close_click':
                                postUserEvent({
                                    type: 'EarnAccountCreatedEvent',
                                    asset: rechargePreferences.taker,
                                    installationId,
                                })
                                setState({
                                    type: 'configure_recharge',
                                    transaction:
                                        state.configureRechargeTransaction,
                                    configuredEarn:
                                        getConfiguredEarnWithEnabledRecharge({
                                            earn,
                                            rechargePreferences,
                                            cardCurrency: cardConfig.currency,
                                            cardSafeAddress:
                                                cardConfig.lastSeenSafeAddress,
                                        }),
                                })
                                break
                            case 'drag':
                            case 'on_expand_request':
                            case 'on_wrong_network_accepted':
                                throw new ImperativeError(
                                    `should not got this msg in earn holder deploy`,
                                    msg
                                )
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_completed_safe_transaction_close_click':
                            case 'import_keys_button_clicked':
                            case 'on_user_operation_bundled':
                                throw new ImperativeError(
                                    `4337 messages not applicable for keystore type ${keyStore.type}`,
                                    msg
                                )
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        case 'configure_recharge':
            return (
                <SendTransaction
                    sendTransactionRequests={[state.transaction]}
                    sessionPassword={sessionPassword}
                    account={account}
                    network={network}
                    networkRPCMap={networkRPCMap}
                    accounts={accounts}
                    keystores={keystores}
                    networkMap={networkMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    fetchSimulationByRequest={async () => {
                        return {
                            type: 'simulated',
                            simulation: {
                                transaction: {
                                    type: 'earn_recharge_configured',
                                    request: {
                                        cardConfig,
                                        earnHolderAddress:
                                            state.configuredEarn.holder,
                                        rebalancers:
                                            state.configuredEarn.cardRecharge
                                                .type === 'recharge_enabled'
                                                ? state.configuredEarn
                                                      .cardRecharge.rebalancers
                                                : (() => {
                                                      throw new ImperativeError(
                                                          'ConfiguredEarn should always have recharge enabled'
                                                      )
                                                  })(),
                                        threshold:
                                            rechargePreferences.threshold,
                                    },
                                },
                                currencies: {},
                                checks: [],
                            },
                        }
                    }}
                    fetchTransactionResultByRequest={async () => ({
                        transaction: {
                            type: 'earn_recharge_configured',
                            request: {
                                cardConfig,
                                earnHolderAddress: state.configuredEarn.holder,
                                rebalancers:
                                    state.configuredEarn.cardRecharge.type ===
                                    'recharge_enabled'
                                        ? state.configuredEarn.cardRecharge
                                              .rebalancers
                                        : (() => {
                                              throw new ImperativeError(
                                                  'ConfiguredEarn should always have recharge enabled'
                                              )
                                          })(),
                                threshold: rechargePreferences.threshold,
                            },
                        },
                        currencies: {},
                    })}
                    state={{ type: 'maximised' }}
                    actionSource={{
                        type: 'internal',
                        transactionEventSource: 'earnEnableRecharge',
                    }}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    portfolio={null}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_predefined_fee_preset_selected':
                                onMsg(msg)
                                break
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'on_completed_transaction_close_click':
                                onMsg({
                                    type: 'on_recharge_setup_successfully',
                                    earn: state.configuredEarn,
                                })
                                break
                            case 'transaction_submited':
                            case 'cancel_submitted':
                            case 'transaction_request_replaced':
                                noop()
                                break
                            case 'on_minimize_click':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                                onMsg({
                                    type: 'close',
                                })
                                break
                            case 'drag':
                            case 'on_expand_request':
                            case 'on_wrong_network_accepted':
                                throw new ImperativeError(
                                    `should not got this msg in earn holder deploy`,
                                    msg
                                )
                            case 'import_keys_button_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_user_operation_bundled':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_completed_safe_transaction_close_click':
                                throw new ImperativeError(
                                    `4337 messages not applicable for keystore type ${keyStore.type}`,
                                    msg
                                )
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
