import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    ReadonlySignerSelectedOnboardedCardConfig,
    RechargePreferences,
} from '@zeal/domains/Card'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { Earn } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { EnableRechargeForSmartWallet } from './EnableRechargeForSmartWallet'
import { EnableRechargeWithUserInteraction } from './EnableRechargeWithUserInteraction'

type Msg =
    | MsgOf<typeof EnableRechargeForSmartWallet>
    | MsgOf<typeof EnableRechargeWithUserInteraction>

type Props = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    earn: Earn
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    sessionPassword: string
    installationId: string
    signal?: AbortSignal
    rechargePreferences: RechargePreferences
    accountsMap: AccountsMap
    keyStore: CardSlientSignKeyStore
    keyStoreMap: KeyStoreMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    feePresetMap: FeePresetMap
    onMsg: (msg: Msg) => void
}

export const EnableRecharge = ({
    earn,
    rechargePreferences,
    accountsMap,
    networkMap,
    networkRPCMap,
    keyStore,
    sessionPassword,
    defaultCurrencyConfig,
    cardConfig,
    installationId,
    keyStoreMap,
    gasCurrencyPresetMap,
    feePresetMap,
    onMsg,
}: Props) => {
    switch (keyStore.type) {
        case 'private_key_store':
        case 'secret_phrase_key':
            return (
                <EnableRechargeWithUserInteraction
                    earn={earn}
                    keyStore={keyStore}
                    cardConfig={cardConfig}
                    rechargePreferences={rechargePreferences}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    accounts={accountsMap}
                    sessionPassword={sessionPassword}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    feePresetMap={feePresetMap}
                    keystores={keyStoreMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )
        case 'safe_4337':
            return (
                <EnableRechargeForSmartWallet
                    earn={earn}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    keyStore={keyStore}
                    sessionPassword={sessionPassword}
                    installationId={installationId}
                    rechargePreferences={rechargePreferences}
                    cardConfig={cardConfig}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default: {
            return notReachable(keyStore)
        }
    }
}
