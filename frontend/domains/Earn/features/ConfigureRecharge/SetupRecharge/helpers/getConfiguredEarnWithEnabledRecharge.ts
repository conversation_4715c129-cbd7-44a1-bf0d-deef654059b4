import * as Web3 from '@zeal/toolkit/Web3'

import { RechargePreferences } from '@zeal/domains/Card'
import { CryptoCurrency } from '@zeal/domains/Currency'
import { ConfiguredEarn, Earn } from '@zeal/domains/Earn'
import { getTakerByTakerType } from '@zeal/domains/Earn/helpers/getTakerByTakerType'

export const getConfiguredEarnWithEnabledRecharge = ({
    earn,
    rechargePreferences,
    cardCurrency,
    cardSafeAddress,
}: {
    earn: Earn
    rechargePreferences: RechargePreferences
    cardSafeAddress: Web3.address.Address
    cardCurrency: CryptoCurrency
}): ConfiguredEarn => {
    const rebalancer = getTakerByTakerType({
        takerType: rechargePreferences.taker,
        earn,
    })

    const takers = earn.takers.map((taker) =>
        taker.type === rebalancer.type
            ? {
                  type: rebalancer.type,
                  address: rebalancer.address,
                  cryptoCurrency: rebalancer.cryptoCurrency,
                  state: 'deployed' as const,
              }
            : taker
    )

    return {
        type: 'configured',
        holder: earn.holder,
        takerPortfolioMap: earn.takerPortfolioMap,
        takers,
        takerApyMap: earn.takerApyMap,
        cardRecharge: {
            type: 'recharge_enabled',
            rebalancers: [
                {
                    type: rebalancer.type,
                    address: rebalancer.address,
                    cryptoCurrency: rebalancer.cryptoCurrency,
                    state: 'deployed' as const,
                },
            ],
            threshold: rechargePreferences.threshold,
            cardSafeAddress,
            cardCurrency,
        },
    }
}
