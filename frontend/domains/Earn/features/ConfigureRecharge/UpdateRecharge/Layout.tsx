import { useRef, useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Chain } from '@zeal/uikit/Chain'
import { Column } from '@zeal/uikit/Column'
import { DragableList } from '@zeal/uikit/DragableList'
import { Group } from '@zeal/uikit/Group'
import { Header } from '@zeal/uikit/Header'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldCrossOctagon } from '@zeal/uikit/Icon/BoldCrossOctagon'
import { IconButton } from '@zeal/uikit/IconButton'
import { Input } from '@zeal/uikit/Input'
import { FloatInput } from '@zeal/uikit/Input/FloatInput'
import { ListItem } from '@zeal/uikit/ListItem'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { TagsRow } from '@zeal/uikit/TagsRow'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'
import { Toggle } from '@zeal/uikit/Toggle'

import { notReachable } from '@zeal/toolkit'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import * as Web3 from '@zeal/toolkit/Web3'

import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { convertStableCoinCurrencyToFiatCurrency } from '@zeal/domains/Currency/helpers/convertStableCoinCurrencyToFiatCurrency'
import {
    CardRechargeEnabled,
    DeployedTaker,
    Taker,
    TakerApyMap,
    TakerPortfolioMap2,
} from '@zeal/domains/Earn'
import { TakerAPYLabel } from '@zeal/domains/Earn/components/TakerAPYLabel'
import { TakerAvatar } from '@zeal/domains/Earn/components/TakerAvatar'
import { TakerTitle } from '@zeal/domains/Earn/components/TakerTitle'
import { UpdateRechargeRequest } from '@zeal/domains/Earn/helpers/createUpdateRechargeRequest'
import { sumTakerPortfolio } from '@zeal/domains/Earn/helpers/sumEarn'
import { FormattedMoneyCompact } from '@zeal/domains/Money/components/FormattedMoneyCompact'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Form, FormError, INPUT_FRACTION, validate } from './validation'

type Props = {
    cardRecharge: CardRechargeEnabled
    earnTakers: Taker[]
    earnHolderAddress: Web3.address.Address
    takerPortfolioMap: TakerPortfolioMap2
    takerApyMap: TakerApyMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | { type: 'on_disable_recharge_click'; request: UpdateRechargeRequest }
    | {
          type: 'on_update_recharge_submitted'
          request: UpdateRechargeRequest
      }

const TAG_INPUT_AMOUNTS = ['50', '200', '600', '1000']

const sortTakersWithRebalancersOrder = ({
    rebalancers,
    takers,
}: {
    rebalancers: DeployedTaker[]
    takers: DeployedTaker[]
}): Form['rebalancers'] => {
    return takers
        .map((taker) => ({
            taker,
            status: rebalancers.some(
                (rebalancer) => rebalancer.address === taker.address
            )
                ? ('enabled' as const)
                : ('disabled' as const),
        }))
        .toSorted((a, b) => {
            switch (a.status) {
                case 'enabled': {
                    switch (b.status) {
                        case 'enabled':
                            const aIndex = rebalancers.findIndex(
                                (rebalancer) =>
                                    rebalancer.address === a.taker.address
                            )
                            const bIndex = rebalancers.findIndex(
                                (rebalancer) =>
                                    rebalancer.address === b.taker.address
                            )

                            return aIndex - bIndex
                        case 'disabled':
                            return -1
                        /* istanbul ignore next */
                        default:
                            return notReachable(b.status)
                    }
                }

                case 'disabled':
                    return 1
                /* istanbul ignore next */
                default:
                    return notReachable(a.status)
            }
        })
}

const initForm = ({
    earnTakers,
    cardRecharge,
}: {
    cardRecharge: CardRechargeEnabled
    earnTakers: Taker[]
}): Form => {
    const deployedTakers = earnTakers.filter(
        (taker): taker is DeployedTaker => {
            switch (taker.state) {
                case 'not_deployed':
                    return false
                case 'deployed':
                    return true
                /* istanbul ignore next */
                default:
                    return notReachable(taker)
            }
        }
    )

    return {
        threshold: toFixedWithFraction(
            cardRecharge.threshold,
            cardRecharge.cardCurrency.fraction
        ),
        rebalancers: sortTakersWithRebalancersOrder({
            rebalancers: cardRecharge.rebalancers,
            takers: deployedTakers,
        }),
    }
}

export const Layout = ({
    cardRecharge,
    earnTakers,
    cardConfig,
    defaultCurrencyConfig,
    takerPortfolioMap,
    earnHolderAddress,
    takerApyMap,
    onMsg,
}: Props) => {
    const initFormRef = useRef(initForm({ earnTakers, cardRecharge }))
    const [form, setForm] = useState<Form>(() =>
        initForm({ earnTakers, cardRecharge })
    )
    const formResult = validate(
        form,
        cardConfig,
        earnHolderAddress,
        initFormRef.current
    )
    const formErrors = formResult.getFailureReason()

    const cardFiatCurrency = convertStableCoinCurrencyToFiatCurrency({
        cryptoCurrency: cardConfig.currency,
    })

    const [defaultRebalancer] = form.rebalancers
    const defaultTaker = defaultRebalancer.taker

    const onSubmitForm = () => {
        formResult.tap((request) => {
            onMsg({
                type: 'on_update_recharge_submitted',
                request,
            })
        })
    }

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {() => <BackIcon size={24} color="iconDefault" />}
                    </IconButton>
                }
            />
            <Column spacing={16} fill shrink>
                <Header
                    title={
                        <FormattedMessage
                            id="earn.setup_reacharge_form.title"
                            defaultMessage="Auto-Recharge keeps your{br}card at the same balance"
                            values={{
                                br: '\n',
                            }}
                        />
                    }
                />
                <Column spacing={8}>
                    <Row spacing={0} alignX="stretch">
                        <Text
                            color="textSecondary"
                            variant="paragraph"
                            weight="regular"
                        >
                            <FormattedMessage
                                id="earn.setup_reacharge.input.label"
                                defaultMessage="Target card balance"
                            />
                        </Text>
                        <Tertiary
                            size="regular"
                            color="on_light"
                            onClick={() =>
                                onMsg({
                                    type: 'on_disable_recharge_click',
                                    request: {
                                        cardConfig,
                                        rebalancers:
                                            cardRecharge.rebalancers as NonEmptyArray<DeployedTaker>,
                                        threshold: 0n,
                                        earnHolderAddress,
                                    },
                                })
                            }
                        >
                            {({ color, textWeight, textVariant }) => (
                                <>
                                    <BoldCrossOctagon size={16} color={color} />
                                    <Text
                                        color={color}
                                        variant={textVariant}
                                        weight={textWeight}
                                    >
                                        <FormattedMessage
                                            id="earn.setup_reacharge.input.disable.label"
                                            defaultMessage="Disable"
                                        />
                                    </Text>
                                </>
                            )}
                        </Tertiary>
                    </Row>
                    <FloatInput
                        value={form.threshold}
                        prefix={cardFiatCurrency.symbol}
                        fraction={INPUT_FRACTION}
                        onChange={(value) => {
                            setForm({
                                ...form,
                                threshold: value,
                            })
                        }}
                    >
                        {({ value, onChange }) => (
                            <Input
                                variant="regular"
                                value={value}
                                state="normal" // we don't highlight input just disabled CTA
                                type="text"
                                onChange={onChange}
                                onSubmitEditing={onSubmitForm}
                                keyboardType="numeric"
                                placeholder={`${cardFiatCurrency.symbol}0`}
                                autoFocus={false}
                                disabled={false}
                                returnKeyType="go"
                                autoCapitalize="none"
                                spellCheck={false}
                                autoComplete="off"
                            />
                        )}
                    </FloatInput>

                    <TagsRow>
                        {TAG_INPUT_AMOUNTS.map((value) => (
                            <TagsRow.TagButtonStretch
                                key={value}
                                onClick={() => {
                                    setForm({
                                        threshold: value,
                                        rebalancers: form.rebalancers,
                                    })
                                }}
                            >
                                <Text
                                    variant="callout"
                                    weight="regular"
                                    color="textPrimary"
                                >
                                    {`${cardFiatCurrency.symbol}${value}`}
                                </Text>
                            </TagsRow.TagButtonStretch>
                        ))}
                    </TagsRow>
                </Column>

                <Column shrink fill spacing={12}>
                    <Text
                        variant="paragraph"
                        weight="regular"
                        color="textSecondary"
                    >
                        <FormattedMessage
                            id="earn.recharge.select_taker.header"
                            defaultMessage="Recharge card in order from"
                        />
                    </Text>

                    {form.rebalancers.length === 1 ? (
                        <Group scroll variant="default">
                            <ListItem
                                key={defaultTaker.type}
                                size="regular"
                                avatar={({ size }) => (
                                    <TakerAvatar
                                        takerType={defaultTaker.type}
                                        size={size}
                                    />
                                )}
                                variant="default"
                                primaryText={
                                    <TakerTitle takerType={defaultTaker.type} />
                                }
                                shortText={
                                    <Chain>
                                        <FormattedMoneyCompact
                                            money={sumTakerPortfolio({
                                                taker: defaultTaker,
                                                takerPortfolioMap:
                                                    takerPortfolioMap,
                                                defaultCurrencyConfig,
                                            })}
                                        />
                                        <TakerAPYLabel
                                            taker={defaultTaker}
                                            takerApyMap={takerApyMap}
                                        />
                                    </Chain>
                                }
                                aria-current={false}
                            />
                        </Group>
                    ) : (
                        <DragableList
                            data={form.rebalancers}
                            keyExtractor={({ taker }) => taker.type}
                            renderItem={({
                                item: { taker, status },
                                isLast,
                                onDragStart,
                                onDragEnd,
                            }) => {
                                const total = sumTakerPortfolio({
                                    taker,
                                    takerPortfolioMap,
                                    defaultCurrencyConfig,
                                })

                                return (
                                    <DragableList.DragableListItem
                                        aria-current={false}
                                        size="regular"
                                        avatar={({ size }) => (
                                            <TakerAvatar
                                                size={size}
                                                takerType={taker.type}
                                            />
                                        )}
                                        primaryText={
                                            <TakerTitle
                                                takerType={taker.type}
                                            />
                                        }
                                        shortText={
                                            <Chain>
                                                <FormattedMoneyCompact
                                                    money={total}
                                                />
                                                <TakerAPYLabel
                                                    taker={taker}
                                                    takerApyMap={takerApyMap}
                                                />
                                            </Chain>
                                        }
                                        side={{
                                            rightIcon: () => (
                                                <Toggle
                                                    variant="default"
                                                    size="regular"
                                                    title={null}
                                                    disabled={false}
                                                    checked={(() => {
                                                        switch (status) {
                                                            case 'enabled':
                                                                return true
                                                            case 'disabled':
                                                                return false
                                                            /* istanbul ignore next */
                                                            default:
                                                                return notReachable(
                                                                    status
                                                                )
                                                        }
                                                    })()}
                                                    onClick={() => {
                                                        setForm({
                                                            ...form,
                                                            rebalancers:
                                                                form.rebalancers.map(
                                                                    (
                                                                        rebalancer
                                                                    ) => {
                                                                        if (
                                                                            rebalancer
                                                                                .taker
                                                                                .address ===
                                                                            taker.address
                                                                        ) {
                                                                            return {
                                                                                taker,
                                                                                status: (() => {
                                                                                    switch (
                                                                                        status
                                                                                    ) {
                                                                                        case 'enabled':
                                                                                            return 'disabled'
                                                                                        case 'disabled':
                                                                                            return 'enabled'

                                                                                        /* istanbul ignore next */
                                                                                        default:
                                                                                            return notReachable(
                                                                                                status
                                                                                            )
                                                                                    }
                                                                                })(),
                                                                            }
                                                                        }

                                                                        return rebalancer
                                                                    }
                                                                ),
                                                        })
                                                    }}
                                                />
                                            ),
                                        }}
                                        onDragStart={onDragStart}
                                        onDragEnd={onDragEnd}
                                        isLast={isLast}
                                    />
                                )
                            }}
                            onReordered={(reordredRebalancers) =>
                                setForm({
                                    ...form,
                                    rebalancers: reordredRebalancers,
                                })
                            }
                        />
                    )}
                </Column>

                <Actions variant="default">
                    <Button
                        variant="secondary"
                        size="regular"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        <FormattedMessage
                            id="action.close"
                            defaultMessage="Close"
                        />
                    </Button>
                    <Button
                        disabled={!!formErrors?.submit}
                        variant="primary"
                        size="regular"
                        onClick={onSubmitForm}
                    >
                        <CTAText error={formErrors?.submit} />
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}

const CTAText = ({ error }: { error: FormError['submit'] }) => {
    if (!error) {
        return (
            <FormattedMessage
                id="earn.recharge.cta"
                defaultMessage="Save changes"
            />
        )
    }

    switch (error.type) {
        case 'recharge_settings_not_changed':
        case 'enter_amount':
            return (
                <FormattedMessage
                    id="earn.recharge.earn_not_configured.enter_amount.error"
                    defaultMessage="Enter amount"
                />
            )
        case 'all_rebalancers_are_disabled':
            return (
                <FormattedMessage
                    id="earn.recharge.earn_not_configured.enable_some_account.error"
                    defaultMessage="Enable account"
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(error)
    }
}
