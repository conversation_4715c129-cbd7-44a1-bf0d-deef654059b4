import React, { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import {
    CardRechargeEnabled,
    Taker,
    TakerApyMap,
    TakerPortfolioMap2,
} from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { UserEvent } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    cardRecharge: CardRechargeEnabled
    earnTakers: Taker[]
    earnHolderAddress: Web3.address.Address
    takerPortfolioMap: TakerPortfolioMap2
    takerApyMap: TakerApyMap

    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keystores: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    installationId: string
    networkRPCMap: NetworkRPCMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    location: Extract<
        UserEvent,
        { type: 'RechargeConfigFlowEnteredEvent' }
    >['location']
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_earn_recharge_updated'
          }
      >

export const UpdateRecharge = ({
    onMsg,
    installationId,
    keystores,
    networkMap,
    networkRPCMap,
    sessionPassword,
    gasCurrencyPresetMap,
    feePresetMap,
    portfolioMap,
    accountsMap,
    cardRecharge,
    takerPortfolioMap,
    takerApyMap,
    earnTakers,
    earnHolderAddress,
    cardConfig,
    location,
    defaultCurrencyConfig,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    useEffect(() => {
        postUserEvent({
            type: 'RechargeConfigFlowEnteredEvent',
            rechargeState: 'on',
            installationId,
            location,
        })
    }, [installationId, location])

    return (
        <>
            <Layout
                defaultCurrencyConfig={defaultCurrencyConfig}
                cardConfig={cardConfig}
                cardRecharge={cardRecharge}
                earnTakers={earnTakers}
                earnHolderAddress={earnHolderAddress}
                takerPortfolioMap={takerPortfolioMap}
                takerApyMap={takerApyMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            onMsg(msg)
                            break
                        case 'on_disable_recharge_click':
                            setModal({
                                type: 'submit_disable_recharge',
                                request: msg.request,
                            })
                            break
                        case 'on_update_recharge_submitted': {
                            postUserEvent({
                                type: 'RechargeSaveInitiatedEvent',
                                amount: msg.request.threshold.toString(),
                                rechargeAssets: msg.request.rebalancers.map(
                                    (item) => item.type
                                ),
                                installationId,
                            })
                            setModal({
                                type: 'submit_update_recharge',
                                request: msg.request,
                            })
                            break
                        }
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                defaultCurrencyConfig={defaultCurrencyConfig}
                portfolioMap={portfolioMap}
                cardConfig={cardConfig}
                accountsMap={accountsMap}
                keystores={keystores}
                networkMap={networkMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                feePresetMap={feePresetMap}
                installationId={installationId}
                sessionPassword={sessionPassword}
                state={modal}
                networkRPCMap={networkRPCMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                            onMsg(msg)
                            break
                        case 'on_earn_recharge_updated':
                            onMsg(msg)
                            setModal({ type: 'closed' })
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
