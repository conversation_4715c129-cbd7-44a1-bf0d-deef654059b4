import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import {
    failure,
    nonEmptyArray,
    Result,
    shape,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { DeployedTaker } from '@zeal/domains/Earn'
import { UpdateRechargeRequest } from '@zeal/domains/Earn/helpers/createUpdateRechargeRequest'

// in UI user entering threshold in fiat currency that is always 2 fraction, but on form submition we use card crypto currency
export const INPUT_FRACTION = 2

export type Form = {
    threshold: string | null
    rebalancers: {
        taker: DeployedTaker
        status: 'disabled' | 'enabled'
    }[]
}

export type FormError = {
    submit?:
        | ZeroAmountFormError
        | RechargeSettingsNotChangedFormError
        | AllRebalancersAreDisabledFormError
}

type ZeroAmountFormError = {
    type: 'enter_amount'
}
type AllRebalancersAreDisabledFormError = {
    type: 'all_rebalancers_are_disabled'
}
type RechargeSettingsNotChangedFormError = {
    type: 'recharge_settings_not_changed'
}

const validateAmount = (
    form: Form,
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
): Result<ZeroAmountFormError, bigint> => {
    const amount = fromFixedWithFraction(
        form.threshold,
        cardConfig.currency.fraction
    )
    if (amount <= 0n) {
        return failure({ type: 'enter_amount' })
    }
    return success(amount)
}

const validateRebalancersNotAllDisabled = (
    form: Form
): Result<AllRebalancersAreDisabledFormError, NonEmptyArray<DeployedTaker>> =>
    nonEmptyArray(
        form.rebalancers
            .filter((rebalancer) => {
                switch (rebalancer.status) {
                    case 'enabled':
                        return true
                    case 'disabled':
                        return false
                    /* istanbul ignore next */
                    default:
                        return notReachable(rebalancer.status)
                }
            })
            .map(({ taker }) => taker)
    ).mapError(() => ({ type: 'all_rebalancers_are_disabled' }))

export const validate = (
    form: Form,
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig,
    earnHolderAddress: Web3.address.Address,
    initForm: Form
): Result<FormError, UpdateRechargeRequest> => {
    return shape({
        submit: validateFormHasChanged(initForm, form).andThen(() =>
            validateRebalancersNotAllDisabled(form).andThen((rebalancers) =>
                validateAmount(form, cardConfig).map((amount) => ({
                    cardConfig,
                    earnHolderAddress,
                    rebalancers,
                    threshold: amount,
                }))
            )
        ),
    }).map(({ submit }) => submit)
}

const validateFormHasChanged = (
    initForm: Form,
    currentForm: Form
): Result<RechargeSettingsNotChangedFormError, unknown> => {
    const _: Record<keyof Form, true> = {
        // Remember to update this function when new fields are added to the form
        rebalancers: true,
        threshold: true,
    }
    const hasSameThreshold =
        Number(initForm.threshold).toFixed(INPUT_FRACTION) ===
        Number(currentForm.threshold).toFixed(INPUT_FRACTION)

    const currentEnabledRebalancers = currentForm.rebalancers.filter(
        (rebalancer) => {
            switch (rebalancer.status) {
                case 'enabled':
                    return true
                case 'disabled':
                    return false
                /* istanbul ignore next */
                default:
                    return notReachable(rebalancer.status)
            }
        }
    )
    const initEnabledRebalancers = initForm.rebalancers.filter((rebalancer) => {
        switch (rebalancer.status) {
            case 'enabled':
                return true
            case 'disabled':
                return false
            /* istanbul ignore next */
            default:
                return notReachable(rebalancer.status)
        }
    })

    const hasSameRebalancers =
        initEnabledRebalancers.length === currentEnabledRebalancers.length &&
        initEnabledRebalancers.every((reb, index) => {
            const currentRebalancer = currentEnabledRebalancers[index]
            return (
                currentRebalancer &&
                reb.taker.address === currentRebalancer.taker.address &&
                reb.status === currentRebalancer.status
            )
        })

    return hasSameThreshold && hasSameRebalancers
        ? failure({
              type: 'recharge_settings_not_changed',
          })
        : success(undefined)
}
