import { useEffect, useRef } from 'react'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'

import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { CardRechargeEnabled, ConfiguredEarn } from '@zeal/domains/Earn'
import { fetchLastRechargeTransactionHash } from '@zeal/domains/Earn/api/fetchLastRechargeTransactionHash'
import { captureError } from '@zeal/domains/Error/helpers/captureError'

import { RechargeAnimationTag } from './RechargeAnimationTag'
import { RechargeOnTag } from './RechargeOnTag'

type Props = {
    recharge: CardRechargeEnabled
    earn: ConfiguredEarn
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | {
          type: 'on_earn_last_recharge_transaction_hash_loaded'
          cardConfig: ReadonlySignerSelectedOnboardedCardConfig
      }
    | MsgOf<typeof RechargeOnTag>
    | MsgOf<typeof RechargeAnimationTag>

export const DataLoader = ({ recharge, cardConfig, earn, onMsg }: Props) => {
    const [loadable, _] = useLoadableData(fetchLastRechargeTransactionHash, {
        type: 'loading',
        params: {
            recharge,
        },
    })
    const initialRechargeTrxHash = useRef(
        cardConfig.lastRechargeTransactionHash
    )

    const liveOnMsg = useLiveRef(onMsg)
    const liveCardConfig = useLiveRef(cardConfig)

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
                break
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_earn_last_recharge_transaction_hash_loaded',
                    cardConfig: {
                        ...liveCardConfig.current,
                        lastRechargeTransactionHash: loadable.data,
                    },
                })
                break
            case 'error':
                captureError(loadable.error)
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [liveCardConfig, liveOnMsg, loadable])

    switch (loadable.type) {
        case 'loaded': {
            if (
                initialRechargeTrxHash.current &&
                loadable.data !== initialRechargeTrxHash.current
            ) {
                return (
                    <RechargeAnimationTag
                        cardRecharge={recharge}
                        earn={earn}
                        onMsg={onMsg}
                    />
                )
            }
            return (
                <RechargeOnTag
                    cardRecharge={recharge}
                    earn={earn}
                    onMsg={onMsg}
                />
            )
        }
        case 'loading':
        case 'error':
            return (
                <RechargeOnTag
                    cardRecharge={recharge}
                    earn={earn}
                    onMsg={onMsg}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
