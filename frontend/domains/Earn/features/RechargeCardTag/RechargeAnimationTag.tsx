import React, { useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { Animation } from '@zeal/uikit/Animation'
import { CardWidgetTag } from '@zeal/uikit/CardWidget/CardWidgetTag'
import { RechargeLightning } from '@zeal/uikit/Icon/RechargeLightning'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'

import { CardRechargeEnabled, ConfiguredEarn } from '@zeal/domains/Earn'

const REACHARGING_ANIMATION_DURATION_MS = 1000

type Msg = {
    type: 'on_enabled_recharge_card_tag_clicked'
    cardRecharge: CardRechargeEnabled
    earn: ConfiguredEarn
}

type Props = {
    cardRecharge: CardRechargeEnabled
    earn: ConfiguredEarn
    onMsg: (msg: Msg) => void
}

type State = { type: 'recharging' } | { type: 'recharged' }

export const RechargeAnimationTag = ({ onMsg, cardRecharge, earn }: Props) => {
    const [state, setState] = useState<State>({ type: 'recharging' })

    switch (state.type) {
        case 'recharging':
            return (
                <CardWidgetTag
                    variant="loading"
                    onClick={() =>
                        onMsg({
                            type: 'on_enabled_recharge_card_tag_clicked',
                            cardRecharge,
                            earn,
                        })
                    }
                >
                    <Text variant="paragraph" weight="regular" color="gray100">
                        <FormattedMessage
                            defaultMessage="Recharging"
                            id="earn.recharge_card_tag.recharging"
                        />
                    </Text>
                    <Animation
                        size={16}
                        animation="radial-progress"
                        loop={false}
                        durationMs={REACHARGING_ANIMATION_DURATION_MS}
                        onAnimationEvent={(event) => {
                            switch (event) {
                                case 'complete':
                                    setState({ type: 'recharged' })
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(event)
                            }
                        }}
                    />
                </CardWidgetTag>
            )
        case 'recharged':
            return (
                <CardWidgetTag
                    variant="success"
                    onClick={() =>
                        onMsg({
                            type: 'on_enabled_recharge_card_tag_clicked',
                            cardRecharge,
                            earn,
                        })
                    }
                >
                    <Text variant="paragraph" weight="regular" color="gray100">
                        <FormattedMessage
                            defaultMessage="Recharged"
                            id="earn.recharge_card_tag.recharged"
                        />
                    </Text>
                    <RechargeLightning size={16} color="gray100" />
                </CardWidgetTag>
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
