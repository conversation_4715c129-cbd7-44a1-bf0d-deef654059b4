import React from 'react'
import { FormattedMessage } from 'react-intl'

import { CardWidgetTag } from '@zeal/uikit/CardWidget/CardWidgetTag'
import { RechargeLightning } from '@zeal/uikit/Icon/RechargeLightning'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { Earn } from '@zeal/domains/Earn'

import { DataLoader } from './DataLoader'

type Props = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    earn: Earn
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'on_disabled_recharge_card_tag_clicked' }
    | MsgOf<typeof DataLoader>

export const RechargeCardTag = ({ earn, cardConfig, onMsg }: Props) => {
    switch (earn.type) {
        case 'not_configured':
            return (
                <CardWidgetTag
                    variant="neutral"
                    onClick={() =>
                        onMsg({ type: 'on_disabled_recharge_card_tag_clicked' })
                    }
                >
                    <Text variant="paragraph" weight="regular" color="gray20">
                        <FormattedMessage
                            defaultMessage="Auto-Recharge"
                            id="earn.recharge_card_tag.recharge_not_configured"
                        />
                    </Text>
                    <RechargeLightning size={16} color="gray20" />
                </CardWidgetTag>
            )
        case 'configured':
            switch (earn.cardRecharge.type) {
                case 'recharge_disabled':
                    return (
                        <CardWidgetTag
                            variant="neutral"
                            onClick={() =>
                                onMsg({
                                    type: 'on_disabled_recharge_card_tag_clicked',
                                })
                            }
                        >
                            <Text
                                variant="paragraph"
                                weight="regular"
                                color="gray20"
                            >
                                <FormattedMessage
                                    defaultMessage="Recharge off"
                                    id="earn.recharge_card_tag.recharge_off"
                                />
                            </Text>
                            <RechargeLightning size={16} color="gray20" />
                        </CardWidgetTag>
                    )
                case 'recharge_enabled':
                    return (
                        <DataLoader
                            onMsg={onMsg}
                            cardConfig={cardConfig}
                            recharge={earn.cardRecharge}
                            earn={earn}
                        />
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(earn.cardRecharge)
            }
        /* istanbul ignore next */
        default:
            return notReachable(earn)
    }
}
