import { FormattedMessage, useIntl } from 'react-intl'

import { getLanguageRule } from '@zeal/uikit/Language'
import { useLanguage } from '@zeal/uikit/Language/LanguageContext'
import { ScreenStatus } from '@zeal/uikit/ScreenStatus'
import { Skeleton } from '@zeal/uikit/Skeleton'
import { Toggle } from '@zeal/uikit/Toggle'

import { notReachable } from '@zeal/toolkit'
import { unsafe_toNumberWithFraction } from '@zeal/toolkit/BigInt'
import { useCurrentTimestamp } from '@zeal/toolkit/Date/useCurrentTimestamp'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { getFormattedPercentage } from '@zeal/toolkit/Percentage'

import { Account } from '@zeal/domains/Account'
import {
    CardConfig,
    CardSlientSignKeyStore,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    CardRechargeEnabled,
    ConfiguredEarn,
    Earnings,
    TakerPortfolioMap2,
    TotalEarningsInDefaultCurrencyMap,
} from '@zeal/domains/Earn'
import { TakerTransactionsMap } from '@zeal/domains/Earn/api/fetchTakerTransactionsMap'
import { calculateTotalEarningsInDefaultCurrency } from '@zeal/domains/Earn/helpers/calculateTotalEarningsInDefaultCurrency'
import { calculateWeightedAPY } from '@zeal/domains/Earn/helpers/calculateWeightedAPY'
import { sumEarnWithEarningsPerMS } from '@zeal/domains/Earn/helpers/sumEarn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { FiatMoney } from '@zeal/domains/Money'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { FormattedMoneyWithParts } from '@zeal/domains/Money/components/FormattedMoneyWithParts'
import { addCurrencySymbolToFormatedMoneyParts } from '@zeal/domains/Money/helpers/addCurrencySymbolToFormatedMoney'
import { getTickingDecimalsNeeded } from '@zeal/domains/Money/helpers/getTickingDecimalsNeeded'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Msg =
    | {
          type: 'on_edit_recharge_click'
          cardConfig: ReadonlySignerSelectedOnboardedCardConfig
          cardRecharge: CardRechargeEnabled
          keyStore: CardSlientSignKeyStore
      }
    | {
          type: 'on_setup_recharge_click'
          cardConfig: ReadonlySignerSelectedOnboardedCardConfig
          keyStore: CardSlientSignKeyStore
      }

type Pollable = PollableData<
    {
        takerTransactionsMap: TakerTransactionsMap
        takerPortfolioMap: TakerPortfolioMap2
    },
    unknown
>

type Props = {
    account: Account
    keyStoreMap: KeyStoreMap
    cardConfig: CardConfig
    earn: ConfiguredEarn
    pollable: Pollable
    defaultCurrencyConfig: DefaultCurrencyConfig
    totalEarningsInDefaultCurrencyMap: TotalEarningsInDefaultCurrencyMap
    onMsg: (msg: Msg) => void
}

export const Header = ({
    pollable,
    account,
    cardConfig,
    earn,
    defaultCurrencyConfig,
    keyStoreMap,
    totalEarningsInDefaultCurrencyMap,
    onMsg,
}: Props) => {
    return (
        <ScreenStatus
            title={
                <HeaderTotalBalanceTicking pollable={pollable} earn={earn} />
            }
            subtitle={
                <FormattedMessage
                    id="earn.view_earn.total_balance_label"
                    defaultMessage="Earning {percentage} per year"
                    values={{
                        percentage: getFormattedPercentage(
                            calculateWeightedAPY({
                                earn,
                                defaultCurrencyConfig,
                            })
                        ),
                    }}
                />
            }
            listItems={
                <>
                    <ScreenStatusTotalEarnings
                        pollable={pollable}
                        cachedTotalEarningsInDefaultCurrency={
                            totalEarningsInDefaultCurrencyMap[
                                account.address
                            ] ?? null
                        }
                    />
                    <ScreenStatusRechargeListItem
                        earn={earn}
                        earnOwner={account}
                        cardConfig={cardConfig}
                        keyStoreMap={keyStoreMap}
                        onMsg={onMsg}
                    />
                </>
            }
        />
    )
}

const MINIMUM_EARNING_DIGITS = 14

const HeaderTotalBalanceTicking = ({
    earn,
    pollable,
}: {
    earn: ConfiguredEarn
    pollable: Pollable
}) => {
    const { formatNumber } = useIntl()
    const { currentSelectedLanguage } = useLanguage()
    const rule = getLanguageRule(currentSelectedLanguage)
    const takerPortfolioMap = (() => {
        switch (pollable.type) {
            case 'loaded':
            case 'reloading':
            case 'subsequent_failed':
                return pollable.data.takerPortfolioMap

            case 'loading':
            case 'error':
                return earn.takerPortfolioMap
            /* istanbul ignore next */
            default:
                return notReachable(pollable)
        }
    })()

    const rerenderInterval = 300
    const _rerender = useCurrentTimestamp({
        refreshIntervalMs: rerenderInterval,
    })

    const totalBalanceInDefaultCurrency = sumEarnWithEarningsPerMS({
        takerPortfolioMap,
    })

    if (totalBalanceInDefaultCurrency) {
        const decimalPlacesNeeded = getTickingDecimalsNeeded({
            earningsPerMs: totalBalanceInDefaultCurrency.earningsPerMs,
            tickIntervalMs: rerenderInterval,
        })

        const amount = unsafe_toNumberWithFraction(
            totalBalanceInDefaultCurrency.totalBalance.amount,
            totalBalanceInDefaultCurrency.totalBalance.currency.fraction
        )
        const decimalsPlacesNeeded =
            decimalPlacesNeeded > MINIMUM_EARNING_DIGITS
                ? MINIMUM_EARNING_DIGITS
                : decimalPlacesNeeded

        const [integer, decimals] = formatNumber(amount, {
            maximumFractionDigits: decimalsPlacesNeeded,
            minimumFractionDigits: decimalsPlacesNeeded,
        }).split(rule.decimalSeparator)

        const { formattedIntPart, formattedDecimalsWithSeparator } =
            addCurrencySymbolToFormatedMoneyParts({
                formattedIntPartWithoutSymbol: integer,
                formattedDesimalsPartWithoutSymbol: decimals,
                rule,
                currency: totalBalanceInDefaultCurrency.totalBalance.currency,
            })

        return (
            <FormattedMoneyWithParts
                intPart={{
                    formattedIntPart,
                    variant: 'largeTitle',
                    color: 'gray5',
                    weight: 'medium',
                }}
                decimalsWithSeparator={
                    formattedDecimalsWithSeparator
                        ? {
                              formattedDecimalsWithSeparator,
                              variant: 'title2',
                              color: 'gray40',
                              weight: 'medium',
                          }
                        : null
                }
            />
        )
    }

    return null
}

const ScreenStatusTotalEarnings = ({
    cachedTotalEarningsInDefaultCurrency,
    pollable,
}: {
    cachedTotalEarningsInDefaultCurrency: Earnings<FiatMoney> | null
    pollable: Pollable
}) => {
    switch (pollable.type) {
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed': {
            const totalEarningsInDefaultCurrency =
                calculateTotalEarningsInDefaultCurrency({
                    takerPortfolioMap: pollable.data.takerPortfolioMap,
                    transactionsMap: pollable.data.takerTransactionsMap,
                })

            return (
                totalEarningsInDefaultCurrency && (
                    <ScreenStatus.ListItem
                        leftText={
                            <FormattedMessage
                                id="earn.view_earn.total_earnings_label"
                                defaultMessage="Total earnings"
                            />
                        }
                        rightText={
                            <FormattedMoneyPrecise
                                withSymbol
                                sign={null}
                                money={totalEarningsInDefaultCurrency.amount}
                            />
                        }
                    />
                )
            )
        }
        case 'loading':
        case 'error':
            if (!cachedTotalEarningsInDefaultCurrency) {
                return (
                    <ScreenStatus.ListItem
                        leftText={
                            <FormattedMessage
                                id="earn.view_earn.total_earnings_label"
                                defaultMessage="Total earnings"
                            />
                        }
                        rightText={
                            <Skeleton
                                variant="default"
                                width={70}
                                height={18}
                            />
                        }
                    />
                )
            }

            return (
                <ScreenStatus.ListItem
                    leftText={
                        <FormattedMessage
                            id="earn.view_earn.total_earnings_label"
                            defaultMessage="Total earnings"
                        />
                    }
                    rightText={
                        <FormattedMoneyPrecise
                            withSymbol
                            sign={null}
                            money={cachedTotalEarningsInDefaultCurrency.amount}
                        />
                    }
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}

const ScreenStatusRechargeListItem = ({
    earn,
    earnOwner,
    cardConfig,
    keyStoreMap,
    onMsg,
}: {
    earnOwner: Account
    earn: ConfiguredEarn
    keyStoreMap: KeyStoreMap
    cardConfig: CardConfig
    onMsg: (msg: Msg) => void
}) => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
        case 'card_readonly_signer_address_is_selected':
            return null
        case 'card_readonly_signer_address_is_selected_fully_onboarded': {
            if (cardConfig.readonlySignerAddress !== earnOwner.address) {
                return null
            }

            const keyStore = getKeyStore({
                address: cardConfig.readonlySignerAddress,
                keyStoreMap,
            })

            switch (keyStore.type) {
                case 'private_key_store':
                case 'secret_phrase_key':
                case 'safe_4337':
                    return (
                        <ScreenStatus.ListItem
                            leftText={
                                <FormattedMessage
                                    id="earn.view_earn.card_recharge"
                                    defaultMessage="Card Recharge"
                                />
                            }
                            rightText={(() => {
                                const { cardRecharge } = earn

                                switch (cardRecharge.type) {
                                    case 'recharge_disabled':
                                        return (
                                            <Toggle
                                                size="regular"
                                                variant="orange"
                                                title={
                                                    <FormattedMessage
                                                        id="earn.view_earn.card_rechard_off"
                                                        defaultMessage="Off"
                                                    />
                                                }
                                                checked={false}
                                                onClick={() => {
                                                    onMsg({
                                                        type: 'on_setup_recharge_click',
                                                        cardConfig,
                                                        keyStore,
                                                    })
                                                }}
                                            />
                                        )
                                    case 'recharge_enabled':
                                        return (
                                            <Toggle
                                                size="regular"
                                                variant="orange"
                                                title={
                                                    <FormattedMessage
                                                        id="earn.view_earn.card_rechard_on"
                                                        defaultMessage="On"
                                                    />
                                                }
                                                checked
                                                onClick={() => {
                                                    onMsg({
                                                        type: 'on_edit_recharge_click',
                                                        cardConfig,
                                                        cardRecharge,
                                                        keyStore,
                                                    })
                                                }}
                                            />
                                        )
                                    default:
                                        return notReachable(cardRecharge)
                                }
                            })()}
                        />
                    )
                case 'ledger':
                case 'trezor':
                case 'track_only':
                    return null

                /* istanbul ignore next */
                default:
                    return notReachable(keyStore)
            }
        }
        /* istanbul ignore next */
        default:
            return notReachable(cardConfig)
    }
}
