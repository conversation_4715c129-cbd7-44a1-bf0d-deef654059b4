import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import * as Web3 from '@zeal/toolkit/Web3'

import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { CryptoCurrency } from '@zeal/domains/Currency'
import {
    GNOSIS_EURE,
    GNOSIS_EURE_V2,
    GNOSIS_GBPE,
    GNOSIS_GBPE_V2,
} from '@zeal/domains/Currency/constants'
import { DeployedTaker } from '@zeal/domains/Earn'
import { HOLDER_ABI } from '@zeal/domains/Earn/constants'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'

export type UpdateRechargeRequest = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    earnHolderAddress: Web3.address.Address
    rebalancers: NonEmptyArray<DeployedTaker>
    threshold: bigint
}

const patchCurrency = (currency: CryptoCurrency): CryptoCurrency => {
    switch (true) {
        case currency.address === GNOSIS_EURE_V2.address:
            return GNOSIS_EURE
        case currency.address === GNOSIS_GBPE_V2.address:
            return GNOSIS_GBPE
        default:
            return currency
    }
}

export const createUpdateRechargeRequest = ({
    cardConfig,
    rebalancers,
    threshold,
    earnHolderAddress,
}: UpdateRechargeRequest): EthSendTransaction => {
    const currency = patchCurrency(cardConfig.currency)

    const trx: EthSendTransaction = {
        id: generateRandomNumber(),
        jsonrpc: '2.0',
        method: 'eth_sendTransaction',
        params: [
            {
                from: cardConfig.readonlySignerAddress,
                data: Web3.abi.encodeFunctionData({
                    abi: HOLDER_ABI,
                    functionName: 'setThresholdWithRecipient',
                    args: [
                        cardConfig.lastSeenSafeAddress,
                        currency.address as `0x${string}`,
                        rebalancers.map(
                            (taker) => taker.address
                        ) as `0x${string}`[],
                        threshold,
                    ],
                }),
                to: earnHolderAddress,
            },
        ],
    }
    return trx
}
