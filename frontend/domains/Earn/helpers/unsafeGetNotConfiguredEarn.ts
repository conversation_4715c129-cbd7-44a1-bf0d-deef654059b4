import { keys } from '@zeal/toolkit/Object'
import * as Web3 from '@zeal/toolkit/Web3'

import { NotConfiguredEarn, NotDeployedTaker } from '@zeal/domains/Earn'
import {
    DEFAULT_TAKER_APY_MAP,
    EARN_ETH_TAKER_USER_CURRENCY,
    EARN_EUR_TAKER_USER_CURRENCY,
    EARN_PRIMARY_INVESTMENT_ASSETS_MAP,
    EARN_USD_TAKER_USER_CURRENCY,
} from '@zeal/domains/Earn/constants'
import { getHolderPredictedAddress } from '@zeal/domains/Earn/helpers/getHolderPredictedAddress'
import { getTakerPredictedAddress } from '@zeal/domains/Earn/helpers/getTakerPredictedAddress'

export const unsafeGetNotConfiguredEarn = ({
    address,
}: {
    address: Web3.address.Address
}): NotConfiguredEarn => {
    const takerTypes = keys(EARN_PRIMARY_INVESTMENT_ASSETS_MAP)

    const holder = getHolderPredictedAddress({
        earnOwnerAddress: address,
    })
    const takers = takerTypes.map<NotDeployedTaker>((takerType) => {
        const takerAddress = getTakerPredictedAddress({ holder, takerType })

        return {
            type: takerType,
            state: 'not_deployed',
            cryptoCurrency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP[takerType],
            address: takerAddress,
        }
    })

    const dataTimestampMs = new Date().getTime()

    return {
        type: 'not_configured',
        takers,
        holder,
        takerPortfolioMap: {
            usd: {
                assetBalance: {
                    amount: 0n,
                    currency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['usd'],
                },
                userCurrencyRate: {
                    base: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['usd'],
                    quote: EARN_USD_TAKER_USER_CURRENCY,
                    rate: 0n,
                },
                userCurrencyToDefaultCurrencyRate: null,
                apy: DEFAULT_TAKER_APY_MAP['usd'],
                dataTimestampMs,
            },
            eur: {
                assetBalance: {
                    amount: 0n,
                    currency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eur'],
                },
                userCurrencyRate: {
                    base: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eur'],
                    quote: EARN_EUR_TAKER_USER_CURRENCY,
                    rate: 0n,
                },
                userCurrencyToDefaultCurrencyRate: null,
                apy: DEFAULT_TAKER_APY_MAP['eur'],
                dataTimestampMs,
            },
            eth: {
                assetBalance: {
                    amount: 0n,
                    currency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eth'],
                },
                userCurrencyRate: {
                    base: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eth'],
                    quote: EARN_ETH_TAKER_USER_CURRENCY,
                    rate: 0n,
                },
                userCurrencyToDefaultCurrencyRate: null,
                apy: DEFAULT_TAKER_APY_MAP['eth'],
                dataTimestampMs,
            },
        },
        takerApyMap: DEFAULT_TAKER_APY_MAP,
    }
}
