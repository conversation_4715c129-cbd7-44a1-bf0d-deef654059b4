import { LanguageSettings } from '@zeal/uikit/Language'
import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { UserAReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { ConnectionMap } from '@zeal/domains/DApp/domains/ConnectionState'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Mode } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { Settings } from '@zeal/domains/Settings/features/Settings'
import {
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

export type Props = {
    mode: Mode
    encryptedPassword: string
    feePresetMap: FeePresetMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    selectedAccount: Account
    notificationsConfig: NotificationsConfig
    isEthereumNetworkFeeWarningSeen: boolean
    connections: ConnectionMap
    customCurrencyMap: CustomCurrencyMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    currencyHiddenMap: CurrencyHiddenMap
    cardConfig: CardConfig
    installationCampaign: string | null
    earnTakerMetrics: EarnTakerMetrics
    state: State
    userAReferralConfig: UserAReferralConfig
    languageSettings: LanguageSettings
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof Settings>

export type State = { type: 'closed' } | { type: 'settings' }

export const Modal = ({
    mode,
    encryptedPassword,
    feePresetMap,
    userAReferralConfig,
    installationCampaign,
    portfolioMap,
    sessionPassword,
    selectedAccount,
    notificationsConfig,
    isEthereumNetworkFeeWarningSeen,
    connections,
    customCurrencyMap,
    gasCurrencyPresetMap,
    accountsMap,
    keyStoreMap,
    networkRPCMap,
    networkMap,
    installationId,
    defaultCurrencyConfig,
    currencyHiddenMap,
    cardConfig,
    earnTakerMetrics,
    languageSettings,
    experimentalMode,
    state,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'settings':
            return (
                <UIModal>
                    <Settings
                        experimentalMode={experimentalMode}
                        languageSettings={languageSettings}
                        userAReferralConfig={userAReferralConfig}
                        earnTakerMetrics={earnTakerMetrics}
                        cardConfig={cardConfig}
                        currencyHiddenMap={currencyHiddenMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        onMsg={onMsg}
                        sessionPassword={sessionPassword}
                        keyStoreMap={keyStoreMap}
                        accountsMap={accountsMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        portfolioMap={portfolioMap}
                        feePresetMap={feePresetMap}
                        connections={connections}
                        customCurrencyMap={customCurrencyMap}
                        encryptedPassword={encryptedPassword}
                        installationCampaign={installationCampaign}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        mode={mode}
                        notificationsConfig={notificationsConfig}
                        selectedAccount={selectedAccount}
                    />
                </UIModal>
            )
        default:
            return notReachable(state)
    }
}
