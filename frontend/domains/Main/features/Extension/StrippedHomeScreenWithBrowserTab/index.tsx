import { useEffect, useState } from 'react'
import { useIntl } from 'react-intl'

import { Column } from '@zeal/uikit/Column'
import { LightSetting } from '@zeal/uikit/Icon/LightSetting'
import { NavBrowseIconOutline } from '@zeal/uikit/Icon/NavBrowseIconOutline'
import { NavBrowseIconSolid } from '@zeal/uikit/Icon/NavBrowseIconSolid'
import { NavCardIconOutline } from '@zeal/uikit/Icon/NavCardIconOutline'
import { NavCardIconSolid } from '@zeal/uikit/Icon/NavCardIconSolid'
import { NavHomeIconOutline } from '@zeal/uikit/Icon/NavHomeIconOutline'
import { NavHomeIconSolid } from '@zeal/uikit/Icon/NavHomeIconSolid'
import { OutlineGift } from '@zeal/uikit/Icon/OutlineGift'
import { SolidGift } from '@zeal/uikit/Icon/SolidGift'
import { IconButton } from '@zeal/uikit/IconButton'
import { LanguageSettings } from '@zeal/uikit/Language'
import {
    RefreshContainer,
    RefreshContainerState,
} from '@zeal/uikit/RefreshContainer'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Spacer } from '@zeal/uikit/Spacer'
import { TabsLayout } from '@zeal/uikit/TabsLayout'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { LoadedReloadableData } from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { keys } from '@zeal/toolkit/Object'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import { openExternalURL } from '@zeal/toolkit/Window'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    FetchPortfolioRequest,
    FetchPortfolioResponse,
} from '@zeal/domains/Account/api/fetchAccounts'
import { ShowBalance } from '@zeal/domains/Account/components/ShowBalance'
import { AppBrowser } from '@zeal/domains/App/features/AppBrowser'
import { CardConfig } from '@zeal/domains/Card'
import {
    GNOSIS_PAY_DASHBOARD_CARD_URL,
    GNOSIS_PAY_DASHBOARD_COMPLETE_CARD_ORDER_URL,
} from '@zeal/domains/Card/constants'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { RewardsTab } from '@zeal/domains/Card/domains/Reward/features/RewardsTab'
import { CardTab } from '@zeal/domains/Card/features/CardTab'
import { GnosisPayOnboardingWidget } from '@zeal/domains/Card/features/GnosisPayOnboardingWidget'
import { cardConfigToUserEventCardOnboardedStatus } from '@zeal/domains/Card/helpers/cardConfigToUserEventCardOnboardedStatus'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { ConnectionMap } from '@zeal/domains/DApp/domains/ConnectionState'
import {
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
} from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import {
    InitialActiveTab,
    Mode,
    StrippedHomeWithBrowserTab,
} from '@zeal/domains/Main'
import { MarketingCarousel } from '@zeal/domains/Main/features/MarketingCarousel'
import { calculateMarketingCarouselState } from '@zeal/domains/Main/helpers/calculateMarketingCarouselState'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2, PortfolioMap } from '@zeal/domains/Portfolio'
import { QuickActionsWidget } from '@zeal/domains/Portfolio/components/QuickActionsWidget'
import {
    BrowserTabState,
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { SupportButton } from '@zeal/domains/Support/features/SupportButton'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { keystoreToUserEventType } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Modal, State as ModalState } from './Modal'

type Props = {
    portfolio: Portfolio2
    account: Account
    initialActiveTab: InitialActiveTab

    experimentalMode: boolean
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    installationId: string
    installationCampaign: string | null
    portfolioMap: PortfolioMap
    isEthereumNetworkFeeWarningSeen: boolean
    networkRPCMap: NetworkRPCMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    customCurrencyMap: CustomCurrencyMap
    notificationsConfig: NotificationsConfig
    cardConfig: CardConfig
    earnTakerMetrics: EarnTakerMetrics
    encryptedPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    appBrowserProviderScript: string | null
    browserTabState: BrowserTabState
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    refreshContainerState: RefreshContainerState
    languageSettings: LanguageSettings
    portfolioLoadable: LoadedReloadableData<
        FetchPortfolioResponse,
        FetchPortfolioRequest
    >
    mode: Mode
    connections: ConnectionMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap

    referralConfig: ReferralConfig
    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof MarketingCarousel>,
          {
              type:
                  | 'add_wallet_clicked'
                  | 'cancel_submitted'
                  | 'hardware_wallet_clicked'
                  | 'import_keys_button_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_account_create_request'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_bank_transfer_selected'
                  | 'on_earn_configured'
                  | 'on_earn_deposit_success'
                  | 'on_recharge_configured'
                  | 'on_earn_withdrawal_success'
                  | 'on_earnings_fetched'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'on_usd_taker_metrics_loaded'
                  | 'track_wallet_clicked'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_swaps_io_swap_request_created'
          }
      >
    | Extract<
          MsgOf<typeof CardTab>,
          {
              type:
                  | 'on_usd_taker_metrics_loaded'
                  | 'add_wallet_clicked'
                  | 'on_bank_transfer_selected'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'track_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_accounts_create_success_animation_finished'
                  | 'hardware_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'cancel_submitted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_earnings_fetched'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_switch_bank_transfer_provider_clicked'
                  | 'on_monerium_deposit_success_go_to_wallet_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'import_card_owner_clicked'
                  | 'on_notifications_config_changed'
                  | 'on_card_disconnected'
                  | 'on_switch_card_new_card_selected'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_get_cashback_currency_clicked'
                  | 'on_card_transactions_fetch_success'
                  | 'on_cashback_loaded'
                  | 'on_earn_last_recharge_transaction_hash_loaded'
                  | 'on_card_onboarded_account_state_received'
                  | 'on_card_readonly_signer_address_selected'
                  | 'on_earn_updated'
                  | 'on_card_onboarded_state_refresh_pulled'
                  | 'on_gnosis_pay_account_created'
                  | 'on_do_bank_transfer_clicked'
                  | 'on_app_rating_submitted'
                  | 'on_cashback_celebration_triggered'
                  | 'on_top_up_transaction_complete_close'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_new_virtual_card_created_successfully'
                  | 'on_dismiss_add_to_wallet_banner_clicked'
                  | 'on_virtual_card_order_created_animation_completed'
                  | 'on_physical_card_activated_info_screen_closed'
                  | 'on_card_onboarded_account_state_card_owner_signer_or_keystore_not_found'
                  | 'on_card_top_up_success'
                  | 'on_card_top_up_banner_dismissed'
                  | 'on_pending_card_top_up_state_changed'
          }
      >
    | MsgOf<typeof AppBrowser>
    | Extract<
          MsgOf<typeof GnosisPayOnboardingWidget>,
          {
              type:
                  | 'on_do_bank_transfer_clicked'
                  | 'on_card_onboarded_account_state_received'
                  | 'on_dissmiss_card_kyc_onboarding_widget_clicked'
          }
      >
    | { type: 'on_portfolio_refresh_pulled' }
    | MsgOf<typeof QuickActionsWidget>
    | Extract<
          MsgOf<typeof RewardsTab>,
          {
              type:
                  | 'on_create_smart_wallet_clicked'
                  | 'on_a_reward_claimed_successfully'
                  | 'on_a_rewards_configured'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_disconnect_dapps_click'
                  | 'on_delete_all_dapps_confirm_click'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_card_owner_clicked'
                  | 'add_wallet_clicked'
                  | 'on_notifications_config_changed'
                  | 'on_card_disconnected'
                  | 'on_switch_card_new_card_selected'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_account_label_change_submit'
                  | 'confirm_account_delete_click'
                  | 'on_rewards_warning_confirm_account_delete_click'
                  | 'on_recovery_kit_setup'
                  | 'on_add_private_key_click'
                  | 'on_account_create_request'
                  | 'track_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'account_item_clicked'
                  | 'on_default_currency_selected'
                  | 'on_lock_zeal_click'
                  | 'on_open_fullscreen_view_click'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_new_virtual_card_created_successfully'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_recharge_configured'
                  | 'import_keys_button_clicked'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_physical_card_activated_info_screen_closed'
                  | 'on_language_settings_language_selected'
                  | 'on_experimental_change_clicked'
                  | 'session_password_decrypted'
          }
      >

const TABS: Record<StrippedHomeWithBrowserTab['type'], true> = {
    portfolio: true,
    card: true,
    rewards: true,
    browse: true,
}

export const StrippedHomeScreenWithBrowserTab = ({
    account,
    portfolio,
    accountsMap,
    cardConfig,
    currencyHiddenMap,
    currencyPinMap,
    customCurrencyMap,
    celebrationConfig,
    appRating,
    defaultCurrencyConfig,
    earnTakerMetrics,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    isEthereumNetworkFeeWarningSeen,
    browserTabState,
    keyStoreMap,
    portfolioLoadable,
    networkMap,
    networkRPCMap,
    initialActiveTab,
    appBrowserProviderScript,
    installationCampaign,
    portfolioMap,
    encryptedPassword,
    notificationsConfig,
    sessionPassword,
    languageSettings,
    refreshContainerState,
    referralConfig,
    connections,
    mode,
    transactionActivitiesCacheMap,
    historicalTakerUserCurrencyRateMap,
    experimentalMode,
    onMsg,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })
    const [tab, setTab] = useState<StrippedHomeWithBrowserTab>(initialActiveTab)
    const tabs = keys(TABS).filter((tab) => {
        switch (tab) {
            case 'portfolio':
            case 'card':
            case 'rewards':
                return true
            case 'browse':
                switch (cardConfig.type) {
                    case 'card_readonly_signer_address_is_not_selected':
                        return false
                    case 'card_readonly_signer_address_is_selected':
                    case 'card_readonly_signer_address_is_selected_fully_onboarded':
                        return true
                    default:
                        return notReachable(cardConfig)
                }
            default:
                return notReachable(tab)
        }
    })

    const { formatMessage } = useIntl()

    useEffect(() => {
        postUserEvent({
            type: 'StrippedHomeScreenEnteredEvent',
            installationId,
        })
    }, [installationId])

    const openUrl = (url: string | null) => {
        switch (ZealPlatform.OS) {
            case 'ios':
            case 'android':
                setTab({ type: 'browse', url: url ? new URL(url) : null })
                break
            case 'web':
                url && openExternalURL(url)
                break
            /* istanbul ignore next */
            default:
                return notReachable(ZealPlatform)
        }
    }

    return (
        <>
            <TabsLayout
                tabs={tabs.map((tabType) => (
                    <TabButton
                        key={tabType}
                        tab={tabType}
                        selected={tabType === tab.type}
                        onClick={() => {
                            switch (tabType) {
                                case 'portfolio':
                                case 'rewards':
                                    setTab({ type: tabType })
                                    break
                                case 'card':
                                    postUserEvent({
                                        type: 'CardEnteredEvent',
                                        location: 'navbar',
                                        cardOnboardedStatus:
                                            cardConfigToUserEventCardOnboardedStatus(
                                                cardConfig
                                            ),
                                        keystoreType: keystoreToUserEventType(
                                            getKeyStore({
                                                keyStoreMap,
                                                address: account.address,
                                            })
                                        ),
                                        installationId,
                                    })
                                    setTab({ type: tabType })
                                    break
                                case 'browse':
                                    setTab({ type: tabType, url: null })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(tabType)
                            }
                        }}
                    />
                ))}
                content={(() => {
                    switch (tab.type) {
                        case 'portfolio':
                            const marketingCarouselState =
                                calculateMarketingCarouselState({
                                    earn: portfolio.earn,
                                    cardConfig,
                                    defaultCurrencyConfig,
                                })

                            return (
                                <Screen
                                    padding="home_screen"
                                    background="default"
                                    onNavigateBack={null}
                                >
                                    <Column spacing={16} fill>
                                        <RefreshContainer
                                            padding="home_screen"
                                            onRefreshPulled={() =>
                                                onMsg({
                                                    type: 'on_portfolio_refresh_pulled',
                                                })
                                            }
                                            state={refreshContainerState}
                                        >
                                            <Column spacing={8}>
                                                <Row
                                                    spacing={8}
                                                    alignY="center"
                                                >
                                                    <ShowBalance
                                                        defaultCurrencyConfig={
                                                            defaultCurrencyConfig
                                                        }
                                                        currencyHiddenMap={
                                                            currencyHiddenMap
                                                        }
                                                        portfolio={
                                                            portfolioLoadable
                                                                .data.portfolio
                                                        }
                                                        onMsg={(msg) => {
                                                            switch (msg.type) {
                                                                case 'on_balance_clicked':
                                                                    setModal({
                                                                        type: 'settings',
                                                                    })
                                                                    break
                                                                /* istanbul ignore next */
                                                                default:
                                                                    return notReachable(
                                                                        msg.type
                                                                    )
                                                            }
                                                        }}
                                                    />
                                                    <Spacer />
                                                    <SupportButton
                                                        variant={{
                                                            type: 'intercom_and_zendesk',
                                                            cardConfig,
                                                        }}
                                                        layoutVariant="icon_button"
                                                        installationId={
                                                            installationId
                                                        }
                                                        location="stripped_home"
                                                    />
                                                    <IconButton
                                                        variant="on_light_bold"
                                                        aria-label={formatMessage(
                                                            {
                                                                id: 'account.widget.settings',
                                                                defaultMessage:
                                                                    'Settings',
                                                            }
                                                        )}
                                                        onClick={(e) => {
                                                            e.stopPropagation()
                                                            setModal({
                                                                type: 'settings',
                                                            })
                                                        }}
                                                    >
                                                        {({ color }) => (
                                                            <LightSetting
                                                                size={24}
                                                                color={color}
                                                            />
                                                        )}
                                                    </IconButton>
                                                </Row>
                                                <QuickActionsWidget
                                                    address={account.address}
                                                    installationId={
                                                        installationId
                                                    }
                                                    onMsg={onMsg}
                                                />

                                                <Column spacing={12}>
                                                    <GnosisPayOnboardingWidget
                                                        accountsMap={
                                                            accountsMap
                                                        }
                                                        cardConfig={cardConfig}
                                                        keyStoreMap={
                                                            keyStoreMap
                                                        }
                                                        networkRPCMap={
                                                            networkRPCMap
                                                        }
                                                        networkMap={networkMap}
                                                        defaultCurrencyConfig={
                                                            defaultCurrencyConfig
                                                        }
                                                        sessionPassword={
                                                            sessionPassword
                                                        }
                                                        installationId={
                                                            installationId
                                                        }
                                                        onMsg={(msg) => {
                                                            switch (msg.type) {
                                                                case 'on_do_bank_transfer_clicked':
                                                                case 'on_card_onboarded_account_state_received':
                                                                case 'on_dissmiss_card_kyc_onboarding_widget_clicked':
                                                                    onMsg(msg)
                                                                    break

                                                                case 'on_activate_free_card_clicked':
                                                                    setTab({
                                                                        type: 'card',
                                                                    })
                                                                    break
                                                                /* istanbul ignore next */
                                                                default:
                                                                    return notReachable(
                                                                        msg
                                                                    )
                                                            }
                                                        }}
                                                    />
                                                    {(() => {
                                                        switch (
                                                            marketingCarouselState.type
                                                        ) {
                                                            case 'hidden':
                                                                return null
                                                            case 'populated':
                                                                return (
                                                                    <MarketingCarousel
                                                                        installationCampaign={
                                                                            installationCampaign
                                                                        }
                                                                        items={
                                                                            marketingCarouselState.items
                                                                        }
                                                                        customCurrencies={
                                                                            customCurrencyMap
                                                                        }
                                                                        earnTakerMetrics={
                                                                            earnTakerMetrics
                                                                        }
                                                                        defaultCurrencyConfig={
                                                                            defaultCurrencyConfig
                                                                        }
                                                                        earnOwner={
                                                                            account
                                                                        }
                                                                        isEthereumNetworkFeeWarningSeen={
                                                                            isEthereumNetworkFeeWarningSeen
                                                                        }
                                                                        cardConfig={
                                                                            cardConfig
                                                                        }
                                                                        networkMap={
                                                                            networkMap
                                                                        }
                                                                        accounts={
                                                                            accountsMap
                                                                        }
                                                                        feePresetMap={
                                                                            feePresetMap
                                                                        }
                                                                        gasCurrencyPresetMap={
                                                                            gasCurrencyPresetMap
                                                                        }
                                                                        networkRPCMap={
                                                                            networkRPCMap
                                                                        }
                                                                        sessionPassword={
                                                                            sessionPassword
                                                                        }
                                                                        installationId={
                                                                            installationId
                                                                        }
                                                                        portfolioMap={
                                                                            portfolioMap
                                                                        }
                                                                        keystores={
                                                                            keyStoreMap
                                                                        }
                                                                        currencyPinMap={
                                                                            currencyPinMap
                                                                        }
                                                                        currencyHiddenMap={
                                                                            currencyHiddenMap
                                                                        }
                                                                        earn={
                                                                            portfolio.earn
                                                                        }
                                                                        onMsg={(
                                                                            msg
                                                                        ) => {
                                                                            switch (
                                                                                msg.type
                                                                            ) {
                                                                                case 'on_card_marketing_card_click':
                                                                                    postUserEvent(
                                                                                        {
                                                                                            type: 'CardEnteredEvent',
                                                                                            location:
                                                                                                'portfolio_screen',
                                                                                            cardOnboardedStatus:
                                                                                                cardConfigToUserEventCardOnboardedStatus(
                                                                                                    cardConfig
                                                                                                ),
                                                                                            keystoreType:
                                                                                                keystoreToUserEventType(
                                                                                                    getKeyStore(
                                                                                                        {
                                                                                                            keyStoreMap,
                                                                                                            address:
                                                                                                                account.address,
                                                                                                        }
                                                                                                    )
                                                                                                ),
                                                                                            installationId,
                                                                                        }
                                                                                    )
                                                                                    setTab(
                                                                                        {
                                                                                            type: 'card',
                                                                                        }
                                                                                    )
                                                                                    break
                                                                                case 'on_usd_taker_metrics_loaded':
                                                                                case 'on_eur_taker_metrics_loaded':
                                                                                case 'on_4337_auto_gas_token_selection_clicked':
                                                                                case 'on_4337_gas_currency_selected':
                                                                                case 'import_keys_button_clicked':
                                                                                case 'on_predefined_fee_preset_selected':
                                                                                case 'on_earn_deposit_success':
                                                                                case 'add_wallet_clicked':
                                                                                case 'on_bank_transfer_selected':
                                                                                case 'track_wallet_clicked':
                                                                                case 'on_account_create_request':
                                                                                case 'on_accounts_create_success_animation_finished':
                                                                                case 'hardware_wallet_clicked':
                                                                                case 'on_add_label_to_track_only_account_during_send':
                                                                                case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                                                                case 'cancel_submitted':
                                                                                case 'on_transaction_completed_splash_animation_screen_competed':
                                                                                case 'transaction_request_replaced':
                                                                                case 'transaction_submited':
                                                                                case 'on_ethereum_network_fee_warning_understand_clicked':
                                                                                case 'on_earn_configured':
                                                                                case 'on_address_scanned':
                                                                                case 'on_address_scanned_and_add_label':
                                                                                case 'on_swaps_io_swap_request_created':
                                                                                    onMsg(
                                                                                        msg
                                                                                    )
                                                                                    break
                                                                                default:
                                                                                    return notReachable(
                                                                                        msg
                                                                                    )
                                                                            }
                                                                        }}
                                                                    />
                                                                )
                                                            default:
                                                                return notReachable(
                                                                    marketingCarouselState
                                                                )
                                                        }
                                                    })()}
                                                </Column>
                                            </Column>
                                        </RefreshContainer>
                                    </Column>
                                </Screen>
                            )

                        case 'card':
                            return (
                                <CardTab
                                    historicalTakerUserCurrencyRateMap={
                                        historicalTakerUserCurrencyRateMap
                                    }
                                    transactionActivitiesCacheMap={
                                        transactionActivitiesCacheMap
                                    }
                                    installationCampaign={installationCampaign}
                                    appRating={appRating}
                                    celebrationConfig={celebrationConfig}
                                    refreshContainerState="refreshed"
                                    accountsMap={accountsMap}
                                    cardConfig={cardConfig}
                                    currencyHiddenMap={currencyHiddenMap}
                                    currencyPinMap={currencyPinMap}
                                    customCurrencyMap={customCurrencyMap}
                                    defaultCurrencyConfig={
                                        defaultCurrencyConfig
                                    }
                                    earnTakerMetrics={earnTakerMetrics}
                                    encryptedPassword={encryptedPassword}
                                    feePresetMap={feePresetMap}
                                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                                    installationId={installationId}
                                    isEthereumNetworkFeeWarningSeen={
                                        isEthereumNetworkFeeWarningSeen
                                    }
                                    keyStoreMap={keyStoreMap}
                                    networkMap={networkMap}
                                    networkRPCMap={networkRPCMap}
                                    notificationsConfig={notificationsConfig}
                                    portfolioMap={portfolioMap}
                                    sessionPassword={sessionPassword}
                                    onMsg={(msg) => {
                                        switch (msg.type) {
                                            case 'on_monerium_deposit_success_go_to_wallet_clicked':
                                                setTab({ type: 'portfolio' })
                                                onMsg(msg)
                                                break
                                            case 'on_card_onboarded_account_state_received':
                                            case 'on_card_imported_success_animation_complete':
                                            case 'on_onboarded_card_imported_success_animation_complete':
                                            case 'on_predefined_fee_preset_selected':
                                            case 'on_account_create_request':
                                            case 'cancel_submitted':
                                            case 'on_4337_auto_gas_token_selection_clicked':
                                            case 'on_4337_gas_currency_selected':
                                            case 'track_wallet_clicked':
                                            case 'add_wallet_clicked':
                                            case 'hardware_wallet_clicked':
                                            case 'import_keys_button_clicked':
                                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                            case 'on_transaction_completed_splash_animation_screen_competed':
                                            case 'transaction_request_replaced':
                                            case 'transaction_submited':
                                            case 'safe_wallet_clicked':
                                            case 'recover_safe_wallet_clicked':
                                            case 'on_select_rpc_click':
                                            case 'on_rpc_change_confirmed':
                                            case 'on_card_transactions_fetch_success':
                                            case 'on_notifications_config_changed':
                                            case 'on_earn_last_recharge_transaction_hash_loaded':
                                            case 'on_get_cashback_currency_clicked':
                                            case 'on_cashback_loaded':
                                            case 'import_card_owner_clicked':
                                            case 'on_earn_updated':
                                            case 'on_card_disconnected':
                                            case 'on_switch_card_new_card_selected':
                                            case 'on_card_import_on_import_keys_clicked':
                                            case 'on_create_smart_wallet_clicked':
                                            case 'on_bank_transfer_selected':
                                            case 'on_accounts_create_success_animation_finished':
                                            case 'on_add_label_to_track_only_account_during_send':
                                            case 'on_ethereum_network_fee_warning_understand_clicked':
                                            case 'on_switch_bank_transfer_provider_clicked':
                                            case 'on_address_scanned':
                                            case 'on_address_scanned_and_add_label':
                                            case 'on_usd_taker_metrics_loaded':
                                            case 'on_eur_taker_metrics_loaded':
                                            case 'on_card_onboarded_state_refresh_pulled':
                                            case 'on_gnosis_pay_account_created':
                                            case 'on_do_bank_transfer_clicked':
                                            case 'on_app_rating_submitted':
                                            case 'on_cashback_celebration_triggered':
                                            case 'on_top_up_transaction_complete_close':
                                            case 'on_swaps_io_swap_request_created':
                                            case 'on_new_virtual_card_created_successfully':
                                            case 'on_dismiss_add_to_wallet_banner_clicked':
                                            case 'on_virtual_card_order_created_animation_completed':
                                            case 'on_physical_card_activated_info_screen_closed':
                                            case 'on_card_onboarded_account_state_card_owner_signer_or_keystore_not_found':
                                            case 'on_card_top_up_success':
                                            case 'on_card_top_up_banner_dismissed':
                                            case 'on_pending_card_top_up_state_changed':
                                                onMsg(msg)
                                                break
                                            case 'on_gnosis_pay_kyc_submitted_animation_complete':
                                                setTab({ type: 'card' })
                                                break
                                            case 'close':
                                                setTab({ type: 'portfolio' })
                                                break

                                            case 'on_activate_existing_monerium_account_click':
                                                openUrl(msg.url)
                                                break
                                            case 'on_fallback_freeze_card_click':
                                            case 'on_card_freeze_toggle_failed':
                                                openUrl(
                                                    GNOSIS_PAY_DASHBOARD_CARD_URL
                                                )
                                                break
                                            case 'on_card_order_redirect_to_gnosis_pay_clicked':
                                                openUrl(
                                                    GNOSIS_PAY_DASHBOARD_COMPLETE_CARD_ORDER_URL
                                                )
                                                break

                                            /* istanbul ignore next */
                                            default:
                                                return notReachable(msg)
                                        }
                                    }}
                                />
                            )

                        case 'rewards':
                            return (
                                <RewardsTab
                                    appRating={appRating}
                                    networkRPCMap={networkRPCMap}
                                    installationId={installationId}
                                    transactionActivitiesCacheMap={
                                        transactionActivitiesCacheMap
                                    }
                                    userAReferralConfig={referralConfig.userA}
                                    accountsMap={accountsMap}
                                    keyStoreMap={keyStoreMap}
                                    selectedAccount={account}
                                    onMsg={(msg) => {
                                        switch (msg.type) {
                                            case 'on_rewards_config_close':
                                                setTab({ type: 'portfolio' })
                                                break

                                            case 'on_create_smart_wallet_clicked':
                                            case 'on_a_reward_claimed_successfully':
                                            case 'on_a_rewards_configured':
                                                onMsg(msg)
                                                break

                                            /* istanbul ignore next */
                                            default:
                                                notReachable(msg)
                                        }
                                    }}
                                />
                            )

                        case 'browse':
                            return (
                                <AppBrowser
                                    appBrowserProviderScript={
                                        appBrowserProviderScript
                                    }
                                    dAppUrl={
                                        tab.url ||
                                        browserTabState.lastVisitedURL
                                    }
                                    onMsg={onMsg}
                                />
                            )

                        /* istanbul ignore next */
                        default:
                            return notReachable(tab)
                    }
                })()}
            />
            <Modal
                experimentalMode={experimentalMode}
                userAReferralConfig={referralConfig.userA}
                state={modal}
                cardConfig={cardConfig}
                earnTakerMetrics={earnTakerMetrics}
                currencyHiddenMap={currencyHiddenMap}
                defaultCurrencyConfig={defaultCurrencyConfig}
                languageSettings={languageSettings}
                installationId={installationId}
                keyStoreMap={keyStoreMap}
                networkMap={networkMap}
                installationCampaign={installationCampaign}
                networkRPCMap={networkRPCMap}
                sessionPassword={sessionPassword}
                accountsMap={accountsMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                portfolioMap={portfolioMap}
                feePresetMap={feePresetMap}
                connections={connections}
                customCurrencyMap={customCurrencyMap}
                encryptedPassword={encryptedPassword}
                mode={mode}
                notificationsConfig={notificationsConfig}
                selectedAccount={account}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_new_physical_card_created_successfully':
                            setModal({ type: 'closed' })
                            break
                        case 'on_disconnect_dapps_click':
                        case 'on_delete_all_dapps_confirm_click':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_card_owner_clicked':
                        case 'add_wallet_clicked':
                        case 'on_notifications_config_changed':
                        case 'on_card_disconnected':
                        case 'on_switch_card_new_card_selected':
                        case 'on_address_scanned':
                        case 'on_address_scanned_and_add_label':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_account_label_change_submit':
                        case 'confirm_account_delete_click':
                        case 'on_rewards_warning_confirm_account_delete_click':
                        case 'on_recovery_kit_setup':
                        case 'on_add_private_key_click':
                        case 'on_account_create_request':
                        case 'track_wallet_clicked':
                        case 'hardware_wallet_clicked':
                        case 'safe_wallet_clicked':
                        case 'recover_safe_wallet_clicked':
                        case 'account_item_clicked':
                        case 'on_default_currency_selected':
                        case 'on_lock_zeal_click':
                        case 'on_open_fullscreen_view_click':
                        case 'on_accounts_create_success_animation_finished':
                        case 'on_new_virtual_card_created_successfully':
                        case 'on_card_import_on_import_keys_clicked':
                        case 'on_card_imported_success_animation_complete':
                        case 'on_onboarded_card_imported_success_animation_complete':
                        case 'on_create_smart_wallet_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_recharge_configured':
                        case 'import_keys_button_clicked':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_physical_card_activated_info_screen_closed':
                        case 'on_language_settings_language_selected':
                        case 'on_experimental_change_clicked':
                        case 'session_password_decrypted':
                            onMsg(msg)
                            break
                        case 'on_card_order_redirect_to_gnosis_pay_clicked':
                            openUrl(
                                GNOSIS_PAY_DASHBOARD_COMPLETE_CARD_ORDER_URL
                            )
                            break
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}

type TabButtonProps = {
    tab: StrippedHomeWithBrowserTab['type']
    selected: boolean
    onClick: () => void
}

const TabButton = ({ tab, selected, onClick }: TabButtonProps) => {
    const { formatMessage } = useIntl()

    switch (tab) {
        case 'portfolio':
            return (
                <IconButton
                    variant="on_light_bold"
                    aria-label={formatMessage({
                        id: 'mainTabs.portfolio.label',
                        defaultMessage: 'Portfolio',
                    })}
                    onClick={onClick}
                >
                    {({ color }) => (
                        <Column spacing={4} alignX="center">
                            {selected ? (
                                <NavHomeIconSolid size={28} color="teal40" />
                            ) : (
                                <NavHomeIconOutline size={28} color={color} />
                            )}
                            <Text
                                color={selected ? 'teal40' : color}
                                variant="caption2"
                                weight="regular"
                            >
                                {formatMessage({
                                    id: 'mainTabs.portfolio.label',
                                    defaultMessage: 'Portfolio',
                                })}
                            </Text>
                        </Column>
                    )}
                </IconButton>
            )

        case 'card':
            return (
                <IconButton
                    variant="on_light_bold"
                    aria-pressed={selected}
                    aria-label={formatMessage({
                        id: 'mainTabs.card.label',
                        defaultMessage: 'Card',
                    })}
                    onClick={onClick}
                >
                    {({ color }) => (
                        <Column spacing={4} alignX="center">
                            {selected ? (
                                <NavCardIconSolid size={28} color="teal40" />
                            ) : (
                                <NavCardIconOutline size={28} color={color} />
                            )}
                            <Text
                                color={selected ? 'teal40' : color}
                                variant="caption2"
                                weight="regular"
                            >
                                {formatMessage({
                                    id: 'mainTabs.card.label',
                                    defaultMessage: 'Card',
                                })}
                            </Text>
                        </Column>
                    )}
                </IconButton>
            )

        case 'rewards': {
            switch (ZealPlatform.OS) {
                case 'android':
                case 'ios':
                    const label = formatMessage({
                        id: 'mainTabs.rewards.label',
                        defaultMessage: 'Rewards',
                    })

                    return (
                        <IconButton
                            variant="on_light_bold"
                            aria-pressed={selected}
                            aria-label={label}
                            onClick={onClick}
                        >
                            {({ color }) => (
                                <Column spacing={4} alignX="center">
                                    {selected ? (
                                        <SolidGift size={28} color="teal40" />
                                    ) : (
                                        <OutlineGift size={28} color={color} />
                                    )}
                                    <Text
                                        color={selected ? 'teal40' : color}
                                        variant="caption2"
                                        weight="regular"
                                    >
                                        {label}
                                    </Text>
                                </Column>
                            )}
                        </IconButton>
                    )
                case 'web':
                    return null
                default:
                    return notReachable(ZealPlatform)
            }
        }

        case 'browse':
            switch (ZealPlatform.OS) {
                case 'android':
                case 'ios':
                    return (
                        <IconButton
                            variant="on_light_bold"
                            aria-pressed={selected}
                            aria-label={formatMessage({
                                id: 'mainTabs.browse.title',
                                defaultMessage: 'Browse',
                            })}
                            onClick={onClick}
                        >
                            {({ color }) => (
                                <Column spacing={4} alignX="center">
                                    {selected ? (
                                        <NavBrowseIconSolid
                                            size={28}
                                            color="teal40"
                                        />
                                    ) : (
                                        <NavBrowseIconOutline
                                            size={28}
                                            color={color}
                                        />
                                    )}
                                    <Text
                                        color={selected ? 'teal40' : color}
                                        variant="caption2"
                                        weight="regular"
                                    >
                                        {formatMessage({
                                            id: 'mainTabs.browse.label',
                                            defaultMessage: 'Browse',
                                        })}
                                    </Text>
                                </Column>
                            )}
                        </IconButton>
                    )

                case 'web':
                    return null
                /* istanbul ignore next */
                default:
                    return notReachable(ZealPlatform)
            }

        /* istanbul ignore next */
        default:
            return notReachable(tab)
    }
}
