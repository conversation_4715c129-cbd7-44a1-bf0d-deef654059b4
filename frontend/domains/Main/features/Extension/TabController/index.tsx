import { useEffect, useState } from 'react'

import { LanguageSettings } from '@zeal/uikit/Language'
import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { LoadedReloadableData } from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    FetchPortfolioRequest,
    FetchPortfolioResponse,
} from '@zeal/domains/Account/api/fetchAccounts'
import { CardConfig } from '@zeal/domains/Card'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SubmittedOfframpTransaction } from '@zeal/domains/Currency/domains/BankTransfer'
import { SubmitedBridgesMap } from '@zeal/domains/Currency/domains/Bridge'
import { SwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { ConnectionMap } from '@zeal/domains/DApp/domains/ConnectionState'
import { WalletConnectInstanceLoadable } from '@zeal/domains/DApp/domains/WalletConnect/api/fetchWalletConnectInstance'
import {
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
    TotalEarningsInDefaultCurrencyMap,
} from '@zeal/domains/Earn'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { InitialActiveTab, Mode } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { clearAllReminders } from '@zeal/domains/Notification/domains/Reminder'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    BankTransferInfo,
    BrowserTabState,
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { Submited } from '@zeal/domains/TransactionRequest'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    mode: Mode
    experimentalMode: boolean
    initialActiveTab: InitialActiveTab
    totalEarningsInDefaultCurrencyMap: TotalEarningsInDefaultCurrencyMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    account: Account
    accounts: AccountsMap
    portfolioMap: PortfolioMap
    customCurrencyMap: CustomCurrencyMap
    portfolioLoadable: LoadedReloadableData<
        FetchPortfolioResponse,
        FetchPortfolioRequest
    >
    keystoreMap: KeyStoreMap
    submitedBridgesMap: SubmitedBridgesMap
    isEthereumNetworkFeeWarningSeen: boolean
    submittedOffRampTransactions: SubmittedOfframpTransaction[]
    installationId: string
    networkMap: NetworkMap
    encryptedPassword: string
    earnTakerMetrics: EarnTakerMetrics
    sessionPassword: string
    transactionRequests: Record<Web3.address.Address, Submited[]>
    networkRPCMap: NetworkRPCMap
    connections: ConnectionMap
    bankTransferInfo: BankTransferInfo
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    feePresetMap: FeePresetMap
    installationCampaign: string | null
    gasCurrencyPresetMap: GasCurrencyPresetMap
    walletConnectInstanceLoadable: WalletConnectInstanceLoadable
    cardConfig: CardConfig
    browserTabState: BrowserTabState
    appBrowserProviderScript: string | null
    notificationsConfig: NotificationsConfig

    defaultCurrencyConfig: DefaultCurrencyConfig
    refreshContainerState: RefreshContainerState
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    languageSettings: LanguageSettings
    referralConfig: ReferralConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_language_settings_language_selected'
                  | 'on_pending_breward_claim_transaction_activity_completed'
                  | 'on_add_funds_click'
                  | 'on_profile_change_confirm_click'
                  | 'reload_button_click'
                  | 'on_portfolio_refresh_pulled'
                  | 'on_recovery_kit_setup'
                  | 'on_account_label_change_submit'
                  | 'confirm_account_delete_click'
                  | 'on_rewards_warning_confirm_account_delete_click'
                  | 'track_wallet_clicked'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'on_account_create_request'
                  | 'account_item_clicked'
                  | 'on_network_item_click'
                  | 'transaction_request_completed'
                  | 'transaction_request_failed'
                  | 'transaction_request_cancelled'
                  | 'on_disconnect_dapps_click'
                  | 'on_delete_all_dapps_confirm_click'
                  | 'on_lock_zeal_click'
                  | 'on_send_nft_click'
                  | 'bridge_completed'
                  | 'on_dismiss_kyc_button_clicked'
                  | 'on_kyc_try_again_clicked'
                  | 'on_rpc_change_confirmed'
                  | 'on_select_rpc_click'
                  | 'on_onramp_success'
                  | 'on_withdrawal_monitor_fiat_transaction_success'
                  | 'on_add_private_key_click'
                  | 'on_open_fullscreen_view_click'
                  | 'on_nba_close_click'
                  | 'on_refresh_button_clicked'
                  | 'on_nba_cta_click'
                  | 'on_zwidget_expand_request'
                  | 'on_send_clicked'
                  | 'on_card_onboarded_account_state_received'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'transaction_failure_accepted'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'cancel_submitted'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'transaction_cancel_failure_accepted'
                  | 'on_browser_url_change'
                  | 'on_browser_closed'
                  | 'on_card_transactions_fetch_success'
                  | 'on_notifications_config_changed'
                  | 'on_asset_added_to_earn'
                  | 'on_earn_withdrawal_success'
                  | 'on_earn_deposit_success'
                  | 'on_earn_last_recharge_transaction_hash_loaded'
                  | 'on_recharge_configured'
                  | 'on_external_earn_deposit_completed_close_click'
                  | 'on_bank_transfer_selected'
                  | 'on_token_pin_click'
                  | 'on_token_un_pin_click'
                  | 'on_token_hide_click'
                  | 'on_token_un_hide_click'
                  | 'on_tokens_refresh_pulled'
                  | 'on_custom_currency_delete_request'
                  | 'on_custom_currency_update_request'
                  | 'on_swap_clicked'
                  | 'on_buy_clicked'
                  | 'on_bridge_clicked'
                  | 'on_get_cashback_currency_clicked'
                  | 'on_cashback_loaded'
                  | 'import_card_owner_clicked'
                  | 'on_gnosis_portfolio_loaded'
                  | 'on_card_disconnected'
                  | 'on_switch_card_new_card_selected'
                  | 'on_earnings_fetched'
                  | 'on_historical_taker_user_currency_rate_fetched'
                  | 'on_swaps_io_swap_requests_fetched'
                  | 'on_swaps_io_transaction_activity_completed'
                  | 'on_swaps_io_transaction_activity_swap_started'
                  | 'on_swaps_io_transaction_activity_failed'
                  | 'on_swaps_io_completed_transaction_activity_clicked'
                  | 'on_swaps_io_pending_transaction_clicked'
                  | 'on_default_currency_selected'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_monerium_deposit_success_go_to_wallet_clicked'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_switch_bank_transfer_provider_clicked'
                  | 'monerium_on_card_disconnected'
                  | 'on_earn_updated'
                  | 'on_earn_configured'
                  | 'on_meta_mask_mode_changed_pupup_refresh_page_clicked'
                  | 'on_dismiss_bridge_widget_click'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_card_onboarded_state_refresh_pulled'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_gnosis_pay_account_created'
                  | 'on_dissmiss_card_kyc_onboarding_widget_clicked'
                  | 'on_app_rating_submitted'
                  | 'on_cashback_celebration_triggered'
                  | 'on_earn_celebration_triggered'
                  | 'on_top_up_transaction_complete_close'
                  | 'on_swaps_io_swap_request_created'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_new_virtual_card_created_successfully'
                  | 'on_dismiss_add_to_wallet_banner_clicked'
                  | 'on_transaction_activities_loaded'
                  | 'on_swaps_io_pending_transaction_activity_completed'
                  | 'on_pending_send_transaction_activity_completed'
                  | 'on_pending_send_transaction_activity_failed'
                  | 'on_virtual_card_order_created_animation_completed'
                  | 'on_dissmiss_card_kyc_onboarded_widget_clicked'
                  | 'on_card_b_reward_dissmiss_clicked'
                  | 'on_pending_breward_claim_transaction_activity_failed'
                  | 'card_breward_claimed'
                  | 'card_brewards_updated'
                  | 'on_a_reward_claimed_successfully'
                  | 'on_a_rewards_configured'
                  | 'on_physical_card_activated_info_screen_closed'
                  | 'on_pending_areward_claim_transaction_activity_completed'
                  | 'on_pending_areward_claim_transaction_activity_failed'
                  | 'on_card_onboarded_account_state_card_owner_signer_or_keystore_not_found'
                  | 'on_pending_card_top_up_state_changed'
                  | 'on_card_top_up_success'
                  | 'on_experimental_change_clicked'
                  | 'session_password_decrypted'
                  | 'on_card_top_up_banner_dismissed'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_completed_transaction_close_click'
                  | 'on_swap_cancelled_close_clicked'
                  | 'on_swap_success_clicked'
                  | 'on_pending_card_balance_timer_completed'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_send_clicked'
                  | 'on_account_create_request'
                  | 'transaction_submited'
                  | 'cancel_submitted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
          }
      >

export const TabController = ({
    mode,
    initialActiveTab,
    accounts,
    languageSettings,
    account,
    networkRPCMap,
    portfolioMap,
    portfolioLoadable,
    keystoreMap,
    installationId,
    encryptedPassword,
    transactionRequests,
    celebrationConfig,
    appRating,
    totalEarningsInDefaultCurrencyMap,
    earnHistoricalTakerUserCurrencyRateMap,
    swapsIOSwapRequestsMap,
    transactionActivitiesCacheMap,
    submittedOffRampTransactions,
    submitedBridgesMap,
    connections,
    sessionPassword,
    installationCampaign,
    customCurrencyMap,
    earnTakerMetrics,
    networkMap,
    bankTransferInfo,
    currencyHiddenMap,
    currencyPinMap,
    feePresetMap,
    gasCurrencyPresetMap,
    walletConnectInstanceLoadable,
    cardConfig,
    browserTabState,
    appBrowserProviderScript,
    notificationsConfig,
    isEthereumNetworkFeeWarningSeen,
    defaultCurrencyConfig,
    refreshContainerState,
    referralConfig,
    experimentalMode,
    onMsg,
}: Props) => {
    const [modalState, setModalState] = useState<ModalState>({ type: 'closed' })

    useEffect(() => {
        clearAllReminders().catch(captureError)
    }, [])

    const onBankTransferSelected = () => {
        switch (bankTransferInfo.type) {
            case 'not_started':
                onMsg({
                    type: 'on_bank_transfer_selected',
                })
                break

            case 'unblock_user_created':
                if (
                    bankTransferInfo.connectedWalletAddress === account.address
                ) {
                    onMsg({
                        type: 'on_bank_transfer_selected',
                    })
                } else {
                    const account =
                        accounts[bankTransferInfo.connectedWalletAddress]

                    if (account) {
                        setModalState({
                            type: 'bank_transfer_setup_for_another_account',
                            bankTransferSetupForAccount: account,
                        })
                    } else {
                        throw new ImperativeError(
                            'Bank transfer was setup with deleted wallet'
                        )
                    }
                }
                break

            /* istanbul ignore next */
            default:
                notReachable(bankTransferInfo)
        }
    }
    return (
        <>
            <Layout
                experimentalMode={experimentalMode}
                languageSettings={languageSettings}
                referralConfig={referralConfig}
                appRating={appRating}
                installationCampaign={installationCampaign}
                celebrationConfig={celebrationConfig}
                refreshContainerState={refreshContainerState}
                defaultCurrencyConfig={defaultCurrencyConfig}
                initialActiveTab={initialActiveTab}
                totalEarningsInDefaultCurrencyMap={
                    totalEarningsInDefaultCurrencyMap
                }
                earnHistoricalTakerUserCurrencyRateMap={
                    earnHistoricalTakerUserCurrencyRateMap
                }
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                transactionActivitiesCacheMap={transactionActivitiesCacheMap}
                earnTakerMetrics={earnTakerMetrics}
                notificationsConfig={notificationsConfig}
                browserTabState={browserTabState}
                cardConfig={cardConfig}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                walletConnectInstanceLoadable={walletConnectInstanceLoadable}
                sessionPassword={sessionPassword}
                installationId={installationId}
                mode={mode}
                customCurrencyMap={customCurrencyMap}
                currencyHiddenMap={currencyHiddenMap}
                currencyPinMap={currencyPinMap}
                networkMap={networkMap}
                submitedBridgesMap={submitedBridgesMap}
                account={account}
                accounts={accounts}
                connections={connections}
                encryptedPassword={encryptedPassword}
                submittedOffRampTransactions={submittedOffRampTransactions}
                keystoreMap={keystoreMap}
                portfolioLoadable={portfolioLoadable}
                portfolioMap={portfolioMap}
                networkRPCMap={networkRPCMap}
                transactionRequests={transactionRequests}
                bankTransferInfo={bankTransferInfo}
                appBrowserProviderScript={appBrowserProviderScript}
                swapsIOSwapRequestsMap={swapsIOSwapRequestsMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_transaction_request_widget_click':
                            setModalState({
                                type: 'submitted_transaction_request_status_popup',
                                transactionRequest: msg.transactionRequest,
                                keyStore: msg.keyStore,
                            })
                            break

                        case 'on_bridge_submitted_click':
                            setModalState({
                                type: 'bridge_submitted_status_popup',
                                bridgeSubmitted: msg.bridgeSubmitted,
                            })
                            break

                        case 'on_bank_transfer_selected':
                        case 'on_do_bank_transfer_clicked':
                            onBankTransferSelected()
                            break

                        case 'on_add_funds_click':
                        case 'on_send_clicked':
                        case 'on_swap_clicked':
                        case 'on_bridge_clicked':
                        case 'on_token_pin_click':
                        case 'on_token_un_pin_click':
                        case 'on_token_hide_click':
                        case 'on_token_un_hide_click':
                        case 'on_rpc_change_confirmed':
                        case 'on_select_rpc_click':
                        case 'on_send_nft_click':
                        case 'on_custom_currency_delete_request':
                        case 'on_custom_currency_update_request':
                        case 'on_profile_change_confirm_click':
                        case 'reload_button_click':
                        case 'on_portfolio_refresh_pulled':
                        case 'on_recovery_kit_setup':
                        case 'on_account_label_change_submit':
                        case 'confirm_account_delete_click':
                        case 'on_rewards_warning_confirm_account_delete_click':
                        case 'on_account_create_request':
                        case 'account_item_clicked':
                        case 'transaction_request_completed':
                        case 'transaction_request_failed':
                        case 'on_disconnect_dapps_click':
                        case 'on_delete_all_dapps_confirm_click':
                        case 'on_lock_zeal_click':
                        case 'bridge_completed':
                        case 'track_wallet_clicked':
                        case 'on_dismiss_kyc_button_clicked':
                        case 'on_kyc_try_again_clicked':
                        case 'on_onramp_success':
                        case 'on_withdrawal_monitor_fiat_transaction_success':
                        case 'on_add_private_key_click':
                        case 'on_open_fullscreen_view_click':
                        case 'transaction_request_replaced':
                        case 'on_refresh_button_clicked':
                        case 'on_zwidget_expand_request':
                        case 'add_wallet_clicked':
                        case 'hardware_wallet_clicked':
                        case 'safe_wallet_clicked':
                        case 'recover_safe_wallet_clicked':
                        case 'on_card_onboarded_account_state_received':
                        case 'on_card_imported_success_animation_complete':
                        case 'on_onboarded_card_imported_success_animation_complete':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_failure_accepted':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'cancel_submitted':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'transaction_cancel_failure_accepted':
                        case 'transaction_submited':
                        case 'on_browser_url_change':
                        case 'on_browser_closed':
                        case 'on_card_transactions_fetch_success':
                        case 'on_notifications_config_changed':
                        case 'on_earn_withdrawal_success':
                        case 'on_earn_deposit_success':
                        case 'on_earn_last_recharge_transaction_hash_loaded':
                        case 'on_recharge_configured':
                        case 'on_get_cashback_currency_clicked':
                        case 'on_cashback_loaded':
                        case 'import_card_owner_clicked':
                        case 'on_card_disconnected':
                        case 'on_tokens_refresh_pulled':
                        case 'on_switch_card_new_card_selected':
                        case 'on_earnings_fetched':
                        case 'on_historical_taker_user_currency_rate_fetched':
                        case 'on_swaps_io_swap_requests_fetched':
                        case 'on_swaps_io_transaction_activity_swap_started':
                        case 'on_swaps_io_transaction_activity_completed':
                        case 'on_swaps_io_transaction_activity_failed':
                        case 'on_default_currency_selected':
                        case 'on_card_import_on_import_keys_clicked':
                        case 'on_create_smart_wallet_clicked':
                        case 'on_address_scanned':
                        case 'on_address_scanned_and_add_label':
                        case 'on_monerium_deposit_success_go_to_wallet_clicked':
                        case 'on_accounts_create_success_animation_finished':
                        case 'on_add_label_to_track_only_account_during_send':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_switch_bank_transfer_provider_clicked':
                        case 'on_earn_updated':
                        case 'on_buy_clicked':
                        case 'on_earn_configured':
                        case 'on_meta_mask_mode_changed_pupup_refresh_page_clicked':
                        case 'on_dismiss_bridge_widget_click':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_card_onboarded_state_refresh_pulled':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_gnosis_pay_account_created':
                        case 'on_dissmiss_card_kyc_onboarding_widget_clicked':
                        case 'on_app_rating_submitted':
                        case 'on_cashback_celebration_triggered':
                        case 'on_earn_celebration_triggered':
                        case 'on_top_up_transaction_complete_close':
                        case 'on_swaps_io_swap_request_created':
                        case 'on_new_virtual_card_created_successfully':
                        case 'on_dismiss_add_to_wallet_banner_clicked':
                        case 'on_transaction_activities_loaded':
                        case 'on_pending_send_transaction_activity_completed':
                        case 'on_pending_send_transaction_activity_failed':
                        case 'on_pending_breward_claim_transaction_activity_completed':
                        case 'on_virtual_card_order_created_animation_completed':
                        case 'on_dissmiss_card_kyc_onboarded_widget_clicked':
                        case 'on_card_b_reward_dissmiss_clicked':
                        case 'card_breward_claimed':
                        case 'card_brewards_updated':
                        case 'on_pending_breward_claim_transaction_activity_failed':
                        case 'on_a_reward_claimed_successfully':
                        case 'on_a_rewards_configured':
                        case 'on_physical_card_activated_info_screen_closed':
                        case 'on_pending_areward_claim_transaction_activity_completed':
                        case 'on_pending_areward_claim_transaction_activity_failed':
                        case 'on_card_onboarded_account_state_card_owner_signer_or_keystore_not_found':
                        case 'on_language_settings_language_selected':
                        case 'on_pending_card_top_up_state_changed':
                        case 'on_card_top_up_success':
                        case 'on_experimental_change_clicked':
                        case 'session_password_decrypted':
                        case 'on_card_top_up_banner_dismissed':
                        case 'on_completed_safe_transaction_close_click':
                        case 'on_completed_transaction_close_click':
                        case 'on_swap_cancelled_close_clicked':
                        case 'on_swap_success_clicked':
                        case 'on_pending_card_balance_timer_completed':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />

            <Modal
                defaultCurrencyConfig={defaultCurrencyConfig}
                currencyHiddenMap={currencyHiddenMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                cardConfig={cardConfig}
                installationId={installationId}
                accountsMap={accounts}
                customCurrencyMap={customCurrencyMap}
                sessionPassword={sessionPassword}
                state={modalState}
                account={account}
                keyStoreMap={keystoreMap}
                portfolioMap={portfolioMap}
                onMsg={async (msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_accounts_create_success_animation_finished':
                            setModalState({ type: 'closed' })
                            break

                        case 'on_continue_to_bank_transfer_clicked':
                            onMsg({ type: 'on_bank_transfer_selected' })
                            break

                        case 'bridge_completed':
                        case 'transaction_submited':
                        case 'cancel_submitted':
                        case 'on_account_create_request':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_request_replaced':
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
