import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Button } from '@zeal/uikit/Button'
import { Chain } from '@zeal/uikit/Chain'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { NavCardIconSolid } from '@zeal/uikit/Icon/NavCardIconSolid'
import { IconButton } from '@zeal/uikit/IconButton'
import { ListItemButton } from '@zeal/uikit/ListItem'
import { Popup } from '@zeal/uikit/Popup'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import {
    LoadableData,
    useLoadableData,
} from '@zeal/toolkit/LoadableData/LoadableData'
import { getFormattedPercentage } from '@zeal/toolkit/Percentage'

import {
    CryptoCurrency,
    Currency,
    DefaultCurrency,
} from '@zeal/domains/Currency'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { ConfiguredEarn, DeployedTaker } from '@zeal/domains/Earn'
import { FXRate2 } from '@zeal/domains/FXRate'
import { fetchCrossRate } from '@zeal/domains/FXRate/api/fetchCrossRates'
import {
    applyNullableRate,
    applyRate2,
    mergeRates,
} from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { convertStableCoinToFiat } from '@zeal/domains/Money/helpers/convertStableCoinToFiat'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
type SendTo =
    | {
          type: 'earn'
          earn: ConfiguredEarn
          taker: DeployedTaker
      }
    | {
          type: 'card'
      }

type Props = {
    rate: LoadableData<FXRate2<CryptoCurrency, DefaultCurrency> | null, unknown>
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    recievedMoney: CryptoMoney
    sendTo: SendTo
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_redirect_to_card_clicked' }
    | {
          type: 'on_redirect_to_earn_clicked'
          taker: DeployedTaker
          earn: ConfiguredEarn
      }

export const Layout = ({
    onMsg,
    rate,
    networkMap,
    networkRPCMap,
    recievedMoney,

    sendTo,
}: Props) => {
    const onMakeSpendable = () => {
        switch (sendTo.type) {
            case 'earn':
                onMsg({
                    type: 'on_redirect_to_earn_clicked',
                    earn: sendTo.earn,
                    taker: sendTo.taker,
                })
                break
            case 'card':
                onMsg({
                    type: 'on_redirect_to_card_clicked',
                })
                break

            default:
                notReachable(sendTo)
                break
        }
    }
    const network = findNetworkByHexChainId(
        recievedMoney.currency.networkHexChainId,
        networkMap
    )

    const recievedMoneyInFiat = convertStableCoinToFiat({
        money: recievedMoney,
    })
    return (
        <Popup.Layout onMsg={onMsg}>
            <ActionBar
                right={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <CloseCross size={24} color={color} />}
                    </IconButton>
                }
            />

            <HeaderV2
                title={
                    <FormattedMessage
                        id="makeSpendable.title"
                        defaultMessage="{amount} received"
                        values={{
                            amount: (
                                <FormattedMoneyPrecise
                                    withSymbol
                                    sign={null}
                                    money={recievedMoneyInFiat}
                                />
                            ),
                        }}
                    />
                }
                size="large"
                align="left"
                subtitle={undefined}
            />

            <Popup.Content>
                <Column spacing={8}>
                    <ListItemButton
                        variant="outline"
                        aria-current
                        avatar={({ size }) => (
                            <NavCardIconSolid size={size} color="teal40" />
                        )}
                        disabled={false}
                        background="surface"
                        primaryText={
                            <FormattedMessage
                                id="makeSpendable.cta"
                                defaultMessage="Make spendable"
                            />
                        }
                        shortText={(() => {
                            switch (sendTo.type) {
                                case 'earn':
                                    const apy =
                                        sendTo.earn.takerApyMap[
                                            sendTo.taker.type
                                        ]
                                    const formattedApy =
                                        getFormattedPercentage(apy)
                                    return (
                                        <FormattedMessage
                                            id="makeSpendable.shortText"
                                            defaultMessage="Earning {apy} per year"
                                            values={{ apy: formattedApy }}
                                        />
                                    )
                                case 'card':
                                    return null

                                /* istanbul ignore next */
                                default:
                                    return notReachable(sendTo)
                            }
                        })()}
                        side={{
                            title: (
                                <SideTitle
                                    sendTo={sendTo}
                                    recievedMoney={recievedMoney}
                                    recievedMoneyInFiat={recievedMoneyInFiat}
                                    networkRPCMap={networkRPCMap}
                                    networkMap={networkMap}
                                />
                            ),
                            subtitle: (
                                <MoneyInDetaulCurrency
                                    recievedMoney={recievedMoney}
                                    rate={rate}
                                />
                            ),
                        }}
                        onClick={onMakeSpendable}
                    />

                    <ListItemButton
                        variant="outline"
                        aria-current={false}
                        avatar={({ size }) => (
                            <CurrencyAvatar
                                size={size}
                                currency={recievedMoney.currency}
                                rightBadge={({ size }) => (
                                    <Badge size={size} network={network} />
                                )}
                            />
                        )}
                        disabled={false}
                        background="surface"
                        primaryText={
                            <FormattedMessage
                                id="makeSpendable.holdAsCash"
                                defaultMessage="Hold as cash"
                            />
                        }
                        shortText={
                            <Chain>
                                <Text ellipsis>
                                    {recievedMoney.currency.code}
                                </Text>

                                <Text ellipsis>{network.name}</Text>
                            </Chain>
                        }
                        side={{
                            title: (
                                <FormattedMoneyPrecise
                                    withSymbol
                                    sign={null}
                                    money={recievedMoney}
                                />
                            ),
                            subtitle: (
                                <MoneyInDetaulCurrency
                                    recievedMoney={recievedMoney}
                                    rate={rate}
                                />
                            ),
                        }}
                        onClick={() => {
                            onMsg({ type: 'close' })
                        }}
                    />
                </Column>
            </Popup.Content>
            <Popup.Actions>
                <Button
                    variant="primary"
                    size="regular"
                    onClick={onMakeSpendable}
                >
                    <FormattedMessage
                        id="makeSpendable.cta"
                        defaultMessage="Make spendable"
                    />
                </Button>
            </Popup.Actions>
        </Popup.Layout>
    )
}

const SideTitle = ({
    sendTo,
    recievedMoney,
    recievedMoneyInFiat,
    networkMap,
    networkRPCMap,
}: {
    sendTo: SendTo
    recievedMoney: CryptoMoney
    recievedMoneyInFiat: FiatMoney
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
}) => {
    switch (sendTo.type) {
        case 'earn': {
            return (
                <EarnSideTitle
                    sendTo={sendTo}
                    recievedMoney={recievedMoney}
                    recievedMoneyInFiat={recievedMoneyInFiat}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                />
            )
        }

        case 'card':
            return (
                <FormattedMoneyPrecise
                    withSymbol
                    sign={null}
                    money={recievedMoneyInFiat}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(sendTo)
    }
}

const MoneyInDetaulCurrency = ({
    rate,
    recievedMoney,
}: {
    rate: LoadableData<FXRate2<CryptoCurrency, DefaultCurrency> | null, unknown>
    recievedMoney: CryptoMoney
}) => {
    switch (rate.type) {
        case 'loading':
        case 'error':
            return null
        case 'loaded':
            const money = applyNullableRate({
                baseAmount: recievedMoney,
                rate: rate.data,
            })
            return money ? (
                <FormattedMoneyPrecise withSymbol sign={null} money={money} />
            ) : null
        /* istanbul ignore next */
        default:
            return notReachable(rate)
    }
}

const fetchReceivedMoneyToTakerUserCurrencyRate = async ({
    sendTo,
    recievedMoney,
    recievedMoneyInFiat,
    networkMap,
    networkRPCMap,
}: {
    sendTo: Extract<SendTo, { type: 'earn' }>
    recievedMoney: CryptoMoney
    recievedMoneyInFiat: FiatMoney
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
}): Promise<FXRate2<CryptoCurrency, Currency> | null> => {
    const takerPortfolio = sendTo.earn.takerPortfolioMap[sendTo.taker.type]
    if (!takerPortfolio) {
        return null
    }

    if (
        takerPortfolio.userCurrencyRate.quote.id ===
        recievedMoneyInFiat.currency.id
    ) {
        return {
            base: recievedMoney.currency,
            quote: takerPortfolio.userCurrencyRate.quote,
            rate: 10n ** BigInt(takerPortfolio.userCurrencyRate.quote.fraction),
        }
    }

    const rateToInvestmentAsset = await fetchCrossRate({
        base: recievedMoney.currency,
        quote: takerPortfolio.userCurrencyRate.base,
        networkMap,
        networkRPCMap,
    })

    if (!rateToInvestmentAsset) {
        return null
    }

    return mergeRates({
        rateA: rateToInvestmentAsset,
        rateB: takerPortfolio.userCurrencyRate,
    })
}

const EarnSideTitle = ({
    sendTo,
    recievedMoney,
    recievedMoneyInFiat,
    networkMap,
    networkRPCMap,
}: {
    sendTo: Extract<SendTo, { type: 'earn' }>
    recievedMoney: CryptoMoney
    recievedMoneyInFiat: FiatMoney
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
}) => {
    const [loadable] = useLoadableData(
        fetchReceivedMoneyToTakerUserCurrencyRate,
        {
            type: 'loading',
            params: {
                sendTo,
                recievedMoney,
                recievedMoneyInFiat,
                networkMap,
                networkRPCMap,
            },
        }
    )

    switch (loadable.type) {
        case 'loaded': {
            return (
                <FormattedMoneyPrecise
                    withSymbol
                    sign={null}
                    money={
                        loadable.data
                            ? applyRate2({
                                  baseAmount: recievedMoney,
                                  rate: loadable.data,
                              })
                            : recievedMoney
                    }
                />
            )
        }
        case 'error':
            return (
                <FormattedMoneyPrecise
                    withSymbol
                    sign={null}
                    money={recievedMoney}
                />
            )
        case 'loading':
            return null
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
