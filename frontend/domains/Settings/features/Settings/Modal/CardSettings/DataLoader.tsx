import { useEffect } from 'react'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { fetchGnosisPayAccountState2WithSilentLogin } from '@zeal/domains/Card/api/fetchGnosisPayAccountState'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { unsafeGetNotConfiguredEarn } from '@zeal/domains/Earn/helpers/unsafeGetNotConfiguredEarn'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import {
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Flow } from './Flow'

type Props = {
    earnTakerMetrics: EarnTakerMetrics
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    keyStore: CardSlientSignKeyStore

    notificationsConfig: NotificationsConfig
    encryptedPassword: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    currencyHiddenMap: CurrencyHiddenMap
    customCurrencyMap: CustomCurrencyMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    keyStoreMap: KeyStoreMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    cardReadonlySigner: Account
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof Flow> | { type: 'close' }

export const DataLoader = ({
    onMsg,
    earnTakerMetrics,
    notificationsConfig,
    encryptedPassword,
    networkRPCMap,
    networkMap,
    accountsMap,
    feePresetMap,
    currencyHiddenMap,
    customCurrencyMap,
    gasCurrencyPresetMap,
    portfolioMap,
    sessionPassword,
    keyStoreMap,
    defaultCurrencyConfig,
    cardConfig,
    cardReadonlySigner,
    keyStore,
    installationId,
}: Props) => {
    const [
        gnosisPayOnBoardedStateLoadable,
        setGnosisPayOnBoardedStateLoadable,
    ] = useLoadableData(fetchGnosisPayAccountState2WithSilentLogin, {
        type: 'loading',
        params: {
            sessionPassword,
            keyStore,
            readonlySignerAddress: cardReadonlySigner.address,
            selectedCardId: cardConfig.selectedCardId,
            networkRPCMap,
            networkMap,
            defaultCurrencyConfig,
        },
    })

    const captureErrorOnce = useCaptureErrorOnce()

    const cachedEarn = unsafe_GetPortfolioCache2({
        portfolioMap,
        address: cardReadonlySigner.address,
    })?.earn

    useEffect(() => {
        if (!cachedEarn) {
            captureErrorOnce(
                new ImperativeError(
                    '[Settings] Earn not found in cache for cardReadOnlySigner'
                )
            )
        }
    }, [cachedEarn, captureErrorOnce])

    const earn =
        cachedEarn ||
        unsafeGetNotConfiguredEarn({
            address: cardReadonlySigner.address,
        })

    switch (gnosisPayOnBoardedStateLoadable.type) {
        case 'loading':
            return (
                <LoadingLayout
                    title={null}
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={
                            <ActionBar
                                left={
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => onMsg({ type: 'close' })}
                                    >
                                        {({ color }) => (
                                            <BackIcon size={24} color={color} />
                                        )}
                                    </IconButton>
                                }
                            />
                        }
                        onClose={() => onMsg({ type: 'close' })}
                    />
                    <AppErrorPopup
                        error={parseAppError(
                            gnosisPayOnBoardedStateLoadable.error
                        )}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setGnosisPayOnBoardedStateLoadable({
                                        type: 'loading',
                                        params: gnosisPayOnBoardedStateLoadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        case 'loaded': {
            switch (gnosisPayOnBoardedStateLoadable.data.type) {
                case 'not_onboarded':
                    return (
                        <>
                            <LoadingLayout
                                title={null}
                                actionBar={
                                    <ActionBar
                                        left={
                                            <IconButton
                                                variant="on_light"
                                                onClick={() =>
                                                    onMsg({ type: 'close' })
                                                }
                                            >
                                                {({ color }) => (
                                                    <BackIcon
                                                        size={24}
                                                        color={color}
                                                    />
                                                )}
                                            </IconButton>
                                        }
                                    />
                                }
                                onClose={() => onMsg({ type: 'close' })}
                            />
                        </>
                    )

                case 'onboarded':
                    return (
                        <Flow
                            installationId={installationId}
                            earn={earn}
                            cardReadonlySigner={cardReadonlySigner}
                            gnosisPayAccountOnboardedState={
                                gnosisPayOnBoardedStateLoadable.data
                            }
                            earnTakerMetrics={earnTakerMetrics}
                            sessionPassword={sessionPassword}
                            keyStore={keyStore}
                            keyStoreMap={keyStoreMap}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            currencyHiddenMap={currencyHiddenMap}
                            customCurrencyMap={customCurrencyMap}
                            portfolioMap={portfolioMap}
                            accountsMap={accountsMap}
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            encryptedPassword={encryptedPassword}
                            notificationsConfig={notificationsConfig}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            cardConfig={cardConfig}
                            onMsg={onMsg}
                        />
                    )

                default:
                    return notReachable(gnosisPayOnBoardedStateLoadable.data)
            }
        }

        default:
            return notReachable(gnosisPayOnBoardedStateLoadable)
    }
}
