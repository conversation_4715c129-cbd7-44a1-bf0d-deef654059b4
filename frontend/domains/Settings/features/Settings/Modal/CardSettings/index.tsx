import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { HardwareWalletSupportDrop } from '@zeal/domains/Card/features/CardHardwareWalletSupportDrop'
import { CardOwerNotFoundWallet } from '@zeal/domains/Card/features/CardOwerNotFoundWallet'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { DataLoader } from './DataLoader'

type Props = {
    installationId: string
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    earnTakerMetrics: EarnTakerMetrics
    customCurrencyMap: CustomCurrencyMap

    notificationsConfig: NotificationsConfig
    encryptedPassword: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    currencyHiddenMap: CurrencyHiddenMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    keyStoreMap: KeyStoreMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationCampaign: string | null

    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof DataLoader>
    | MsgOf<typeof HardwareWalletSupportDrop>
    | MsgOf<typeof CardOwerNotFoundWallet>

export const CardSettings = ({
    onMsg,
    installationCampaign,
    earnTakerMetrics,
    customCurrencyMap,
    notificationsConfig,
    encryptedPassword,
    networkRPCMap,
    networkMap,
    accountsMap,
    feePresetMap,
    currencyHiddenMap,
    gasCurrencyPresetMap,
    portfolioMap,
    sessionPassword,
    keyStoreMap,
    defaultCurrencyConfig,
    installationId,
    cardConfig,
}: Props) => {
    const keyStore = getKeyStore({
        keyStoreMap,
        address: cardConfig.readonlySignerAddress,
    })

    const cardReadOwnerSigner = accountsMap[cardConfig.readonlySignerAddress]

    switch (keyStore.type) {
        case 'track_only':
            return (
                <CardOwerNotFoundWallet
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    variant="closable"
                    installationId={installationId}
                    installationCampaign={installationCampaign}
                    cardReadonlySignerAddress={cardConfig.readonlySignerAddress}
                    accountsMap={accountsMap}
                    keystoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    currencyHiddenMap={currencyHiddenMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    onMsg={onMsg}
                />
            )

        case 'ledger':
        case 'trezor':
            return (
                <HardwareWalletSupportDrop
                    variant="closable"
                    installationId={installationId}
                    installationCampaign={installationCampaign}
                    accountsMap={accountsMap}
                    cardReadonlySignerAddress={cardConfig.readonlySignerAddress}
                    keystoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    currencyHiddenMap={currencyHiddenMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    onMsg={onMsg}
                />
            )
        case 'private_key_store':
        case 'secret_phrase_key':
        case 'safe_4337':
            return (
                <DataLoader
                    earnTakerMetrics={earnTakerMetrics}
                    customCurrencyMap={customCurrencyMap}
                    cardReadonlySigner={cardReadOwnerSigner}
                    installationId={installationId}
                    sessionPassword={sessionPassword}
                    keyStore={keyStore}
                    keyStoreMap={keyStoreMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    currencyHiddenMap={currencyHiddenMap}
                    portfolioMap={portfolioMap}
                    accountsMap={accountsMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    encryptedPassword={encryptedPassword}
                    notificationsConfig={notificationsConfig}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    cardConfig={cardConfig}
                    onMsg={onMsg}
                />
            )
        default:
            return notReachable(keyStore)
    }
}
