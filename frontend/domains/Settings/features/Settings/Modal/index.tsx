import { LanguageSettings } from '@zeal/uikit/Language'
import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { SelectTypeOfAccountToAdd } from '@zeal/domains/Account/components/SelectTypeOfAccountToAdd'
import { AddFromSecretPhrase } from '@zeal/domains/Account/features/AddFromSecretPhrase'
import { DetailsView } from '@zeal/domains/Account/features/DetailsView'
import { Manage } from '@zeal/domains/Account/features/Manage'
import {
    CardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { UserAReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { DefaultCurrencySelector } from '@zeal/domains/Currency/components/DefaultCurrencySelector'
import { ConnectionMap } from '@zeal/domains/DApp/domains/ConnectionState'
import { Manage as ManageConnections } from '@zeal/domains/DApp/domains/ConnectionState/features/Manage'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { KeyStoreMap, SecretPhrase } from '@zeal/domains/KeyStore'
import { LanguageSelector } from '@zeal/domains/Language/components/LanguageSelector'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { NotificationsSettings } from '@zeal/domains/Notification/features/NotificationsSettings'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import {
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { CardSettings } from './CardSettings'

type Props = {
    installationId: string
    earnTakerMetrics: EarnTakerMetrics
    state: State
    connections: ConnectionMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    notificationsConfig: NotificationsConfig
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    cardConfig: CardConfig
    customCurrencyMap: CustomCurrencyMap
    encryptedPassword: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    isEthereumNetworkFeeWarningSeen: boolean
    installationCampaign: string | null
    selectedAccount: Account
    userAReferralConfig: UserAReferralConfig
    languageSettings: LanguageSettings
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof ManageConnections>
    | MsgOf<typeof NotificationsSettings>
    | MsgOf<typeof DefaultCurrencySelector>
    | MsgOf<typeof CardSettings>
    | MsgOf<typeof DetailsView>
    | MsgOf<typeof Manage>
    | MsgOf<typeof SelectTypeOfAccountToAdd>
    | MsgOf<typeof AddFromSecretPhrase>
    | MsgOf<typeof LanguageSelector>

export type State =
    | { type: 'closed' }
    | { type: 'manage_connections' }
    | { type: 'notifications_settings' }
    | { type: 'account_details'; address: Web3.address.Address }
    | { type: 'select_account' }
    | { type: 'default_currency_selector' }
    | { type: 'select_type_of_account_to_add' }
    | {
          type: 'add_from_secret_phrase'
          secretPhraseMap: Record<
              string,
              { keystore: SecretPhrase; account: Account }[]
          >
      }
    | {
          type: 'card_settings'
          cardConfig: ReadonlySignerSelectedOnboardedCardConfig
      }
    | { type: 'select_language_selector' }

export const Modal = ({
    state,
    earnTakerMetrics,
    accountsMap,
    userAReferralConfig,
    keyStoreMap,
    portfolioMap,
    connections,
    installationId,
    defaultCurrencyConfig,
    currencyHiddenMap,
    notificationsConfig,
    encryptedPassword,
    networkRPCMap,
    networkMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationCampaign,
    selectedAccount,
    sessionPassword,
    cardConfig,
    customCurrencyMap,
    isEthereumNetworkFeeWarningSeen,
    languageSettings,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null

        case 'select_type_of_account_to_add':
            return <SelectTypeOfAccountToAdd onMsg={onMsg} />

        case 'manage_connections':
            return (
                <UIModal>
                    <ManageConnections
                        installationId={installationId}
                        connections={connections}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'notifications_settings':
            return (
                <UIModal>
                    <NotificationsSettings
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        currencyHiddenMap={currencyHiddenMap}
                        accountsMap={accountsMap}
                        keyStoreMap={keyStoreMap}
                        notificationsConfig={notificationsConfig}
                        portfolioMap={portfolioMap}
                        installationId={installationId}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'default_currency_selector':
            return (
                <UIModal>
                    <DefaultCurrencySelector
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'card_settings':
            return (
                <UIModal>
                    <CardSettings
                        earnTakerMetrics={earnTakerMetrics}
                        customCurrencyMap={customCurrencyMap}
                        cardConfig={state.cardConfig} // TODO :: @max check how it works today
                        installationId={installationId}
                        sessionPassword={sessionPassword}
                        installationCampaign={installationCampaign}
                        keyStoreMap={keyStoreMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        currencyHiddenMap={currencyHiddenMap}
                        portfolioMap={portfolioMap}
                        accountsMap={accountsMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        encryptedPassword={encryptedPassword}
                        notificationsConfig={notificationsConfig}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'account_details':
            return (
                <UIModal>
                    <DetailsView
                        userAReferralConfig={userAReferralConfig}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        networkMap={networkMap}
                        installationId={installationId}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        currencyHiddenMap={currencyHiddenMap}
                        keystoreMap={keyStoreMap}
                        accounts={accountsMap}
                        encryptedPassword={encryptedPassword}
                        portfolio={unsafe_GetPortfolioCache2({
                            address: state.address,
                            portfolioMap,
                        })}
                        account={accountsMap[state.address]}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'select_account':
            return (
                <UIModal>
                    <Manage
                        userAReferralConfig={userAReferralConfig}
                        account={selectedAccount}
                        accounts={accountsMap}
                        cardConfig={cardConfig}
                        currencyHiddenMap={currencyHiddenMap}
                        customCurrencyMap={customCurrencyMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        encryptedPassword={encryptedPassword}
                        installationId={installationId}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        keystoreMap={keyStoreMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        portfolioMap={portfolioMap}
                        sessionPassword={sessionPassword}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'add_from_secret_phrase':
            return (
                <UIModal>
                    <AddFromSecretPhrase
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        currencyHiddenMap={currencyHiddenMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        accountsMap={accountsMap}
                        cardConfig={cardConfig}
                        keystoreMap={keyStoreMap}
                        sessionPassword={sessionPassword}
                        secretPhraseMap={state.secretPhraseMap}
                        customCurrencies={customCurrencyMap}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'select_language_selector':
            return (
                <UIModal>
                    <LanguageSelector
                        languageSettings={languageSettings}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
