import {
    DEFAULT_TAKER_APY_MAP,
    EARN_ETH_TAKER_USER_CURRENCY,
    EARN_EUR_TAKER_USER_CURRENCY,
    EARN_PRIMARY_INVESTMENT_ASSETS_MAP,
    EARN_USD_TAKER_USER_CURRENCY,
} from '@zeal/domains/Earn/constants'

export const localStorageOnboarded: unknown = {
    selectedAddress: '******************************************',
    fetchedAt: '2022-11-07T10:18:45.320Z',
    accounts: {
        '******************************************': {
            label: 'TestWallet',
            address: '******************************************',
        },
    },
    keystoreMap: {},
    encryptedPassword:
        '{"data":"xUj8jWkxJzY5ykrN2XO6fxni1Ee3GJ23Y1mUuIi4asdjG+n04A2kh7Uv1sAmxuEFGctF+/m/jHRlEdwQDMmZo2qjbjsCLzFt1VPb5aYzeWSSFtdO1//taT47athyIEjd4z85686sLw34QDMXcWMD","iv":"AkZbCXliPDuXoNuOPxP7OQ==","salt":"rsJMJo949lv+pgk0jBcCpAEnFtPzrGLPA+ku0aSeIzk="}',
    dApps: {},
    transactionRequests: {},
}

export const portfolioMap: object = {
    '******************************************': {
        currencies: {
            'Polygon|******************************************': {
                type: 'CryptoCurrency',
                id: 'Polygon|******************************************',
                network: 'Polygon',
                address: '******************************************',
                symbol: 'USDC',
                code: 'USDC',
                fraction: 6,
                rateFraction: 6,
                icon: 'https://iw8i6d52oi.execute-api.eu-west-2.amazonaws.com/wallet/image/currency/Polygon|******************************************',
                name: 'USD Coin (PoS)',
            },
            'Ethereum|******************************************': {
                type: 'CryptoCurrency',
                id: 'Ethereum|******************************************',
                symbol: 'ETH',
                code: 'ETH',
                fraction: 18,
                rateFraction: 18,
                icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Ethereum|******************************************',
                name: 'ETH',
                address: '******************************************',
                marketCapRank: 2,
                network: 'Ethereum',
            },
            USD: {
                type: 'FiatCurrency',
                id: 'USD',
                symbol: '$',
                code: 'USD',
                fraction: 18,
                rateFraction: 18,
                icon: 'TODO',
                name: 'USD',
            },
        },
        tokens: [
            {
                network: 'Polygon',
                rate: {
                    base: 'Polygon|******************************************',
                    quote: 'USD',
                    rate: '1000000000000000000',
                },
                address: '******************************************',
                balance: {
                    amount: '157223311',
                    currencyId:
                        'Polygon|******************************************',
                },
                priceInDefaultCurrency: {
                    amount: '157223311000000004096',
                    currencyId: 'USD',
                },
            },
            {
                network: 'Ethereum',
                address: '******************************************',
                rate: {
                    base: 'Ethereum|******************************************',
                    quote: 'USD',
                    rate: '2655990000000000000000',
                },
                balance: {
                    amount: '13641368267331202560',
                    currencyId:
                        'Ethereum|******************************************',
                },
                priceInDefaultCurrency: {
                    amount: '13864136826733120256',
                    currencyId: 'USD',
                },
                marketData: {
                    priceChange24h: {
                        percentage: 0.030652,
                        direction: 'Up',
                    },
                },
                scam: false,
            },
        ],
        apps: [],
        nftCollections: [],
        earn: {
            type: 'not_configured',
            takers: [
                {
                    type: 'usd',
                    state: 'not_deployed',
                    cryptoCurrency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['usd'],
                    address: '******************************************',
                },
                {
                    type: 'eur',
                    state: 'not_deployed',
                    cryptoCurrency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eur'],
                    address: '******************************************',
                },
                {
                    type: 'eth',
                    state: 'not_deployed',
                    cryptoCurrency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eth'],
                    address: '******************************************',
                },
            ],
            holder: '******************************************',
            takerPortfolioMap: {
                usd: {
                    assetBalance: {
                        amount: 0n,
                        currency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['usd'],
                    },
                    userCurrencyRate: {
                        base: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['usd'],
                        quote: EARN_USD_TAKER_USER_CURRENCY,
                        rate: 0n,
                    },
                    userCurrencyToDefaultCurrencyRate: null,
                    apy: DEFAULT_TAKER_APY_MAP['usd'],
                    dataTimestampMs: *************,
                },
                eur: {
                    assetBalance: {
                        amount: 0n,
                        currency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eur'],
                    },
                    userCurrencyRate: {
                        base: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eur'],
                        quote: EARN_EUR_TAKER_USER_CURRENCY,
                        rate: 0n,
                    },
                    userCurrencyToDefaultCurrencyRate: null,
                    apy: DEFAULT_TAKER_APY_MAP['eur'],
                    dataTimestampMs: *************,
                },
                eth: {
                    assetBalance: {
                        amount: 0n,
                        currency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eth'],
                    },
                    userCurrencyRate: {
                        base: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eth'],
                        quote: EARN_ETH_TAKER_USER_CURRENCY,
                        rate: 0n,
                    },
                    userCurrencyToDefaultCurrencyRate: null,
                    apy: DEFAULT_TAKER_APY_MAP['eth'],
                    dataTimestampMs: *************,
                },
            },
            takerApyMap: DEFAULT_TAKER_APY_MAP,
        },
    },
}

export const emptyPortfolioMap: unknown = {
    '******************************************': {
        currencies: {
            USD: {
                type: 'FiatCurrency',
                id: 'USD',
                symbol: '$',
                code: 'USD',
                fraction: 18,
                rateFraction: 18,
                icon: 'TODO',
                name: 'USD',
            },
        },
        tokens: [],
        apps: [],
        nftCollections: [],
        earn: {
            type: 'not_configured',
            takers: [
                {
                    type: 'usd',
                    state: 'not_deployed',
                    cryptoCurrency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['usd'],
                    address: '******************************************',
                },
                {
                    type: 'eur',
                    state: 'not_deployed',
                    cryptoCurrency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eur'],
                    address: '******************************************',
                },
                {
                    type: 'eth',
                    state: 'not_deployed',
                    cryptoCurrency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eth'],
                    address: '******************************************',
                },
            ],
            holder: '******************************************',
            takerPortfolioMap: {
                usd: {
                    assetBalance: {
                        amount: 0n,
                        currency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['usd'],
                    },
                    userCurrencyRate: {
                        base: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['usd'],
                        quote: EARN_USD_TAKER_USER_CURRENCY,
                        rate: 0n,
                    },
                    userCurrencyToDefaultCurrencyRate: null,
                    apy: DEFAULT_TAKER_APY_MAP['usd'],
                    dataTimestampMs: *************,
                },
                eur: {
                    assetBalance: {
                        amount: 0n,
                        currency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eur'],
                    },
                    userCurrencyRate: {
                        base: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eur'],
                        quote: EARN_EUR_TAKER_USER_CURRENCY,
                        rate: 0n,
                    },
                    userCurrencyToDefaultCurrencyRate: null,
                    apy: DEFAULT_TAKER_APY_MAP['eur'],
                    dataTimestampMs: *************,
                },
                eth: {
                    assetBalance: {
                        amount: 0n,
                        currency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eth'],
                    },
                    userCurrencyRate: {
                        base: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eth'],
                        quote: EARN_ETH_TAKER_USER_CURRENCY,
                        rate: 0n,
                    },
                    userCurrencyToDefaultCurrencyRate: null,
                    apy: DEFAULT_TAKER_APY_MAP['eth'],
                    dataTimestampMs: *************,
                },
            },
            takerApyMap: DEFAULT_TAKER_APY_MAP,
        },
    },
}

export const localStorageConnected: unknown = {
    selectedAddress: '******************************************',
    fetchedAt: '2022-11-07T10:18:45.320Z',
    accounts: {
        '******************************************': {
            label: 'TestWallet',
            address: '******************************************',
        },
    },
    keystoreMap: {},
    encryptedPassword:
        '{"data":"xUj8jWkxJzY5ykrN2XO6fxni1Ee3GJ23Y1mUuIi4asdjG+n04A2kh7Uv1sAmxuEFGctF+/m/jHRlEdwQDMmZo2qjbjsCLzFt1VPb5aYzeWSSFtdO1//taT47athyIEjd4z85686sLw34QDMXcWMD","iv":"AkZbCXliPDuXoNuOPxP7OQ==","salt":"rsJMJo949lv+pgk0jBcCpAEnFtPzrGLPA+ku0aSeIzk="}',
    dApps: {
        'dapp1.example.com': {
            type: 'connected',
            dApp: {
                title: 'DEX1',
                avatar: null,
                hostname: 'dapp1.example.com',
            },
            networkHexId: '0x1',
            address: '******************************************',
            connectedAtMs: *************,
            version: 1,
        },
        'dapp2.example.com': {
            type: 'connected',
            version: 1,
            dApp: {
                title: 'DEX2',
                avatar: null,
                hostname: 'dapp2.example.com',
            },
            networkHexId: '0x2',
            address: '0x83f1caAdaBeEC2945b73087F803d404F054Cc2B2',
            connectedAtMs: *************,
        },
        'dapp3.example.com': {
            type: 'connected',
            version: 1,
            dApp: {
                title: 'DEX3',
                avatar: null,
                hostname: 'dapp3.example.com',
            },
            networkHexId: '0x3',
            address: '0x83f1caAdaBeEC2945b73087F803d404F054Cc2B3',
            connectedAtMs: *************,
        },
    },
    transactionRequests: {},
}

export const firstSignUp: unknown = {
    selectedAddress: '******************************************',
    fetchedAt: '2023-02-14T10:02:49.947Z',
    accounts: {
        '******************************************': {
            label: 'Account 1',
            address: '******************************************',
            avatarSrc: null,
        },
    },
    feePresetMap: {
        '0x1': { type: 'Fast' },
        '0x38': { type: 'Fast' },
        '0x89': { type: 'Fast' },
        '0xa': { type: 'Fast' },
    },
    keystoreMap: {
        '******************************************': {
            type: 'secret_phrase_key',
            bip44Path: "m/44'/60'/0'/0/0",
            encryptedPhrase:
                '{"data":"dz8N2xQ89/PcrU2nJ4ylwoQ6afbnA3j2kzaq5bdakugQgmIWeZMf6wynYIVC35EX22tD1x74D3uEaUcCaCirPJZD6tAO84tQ4CZTZ2Z/ZRIXqsLX6KcEHqQVqbZn36Uqunpfvk4BQw==","iv":"AAECAwQFBgcICQoLDA0ODw==","salt":"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA="}',
            confirmed: false,
        },
    },
    encryptedPassword:
        '{"data":"8DBGNJMDoFGVa1M8g/hmjvMMKMEnI7WIiNqXAjpp10dELVvapXL4QnuQOcAAU3ag+1Vgd1nF8MdSBjsVRoUh91pMQ/FCI3NxEu0xcw0qaZijmNG0OK2VObgJfHl44hCKaNdXGT8tzttydJB3cYM5","iv":"AAECAwQFBgcICQoLDA0ODw==","salt":"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA="}',
    dApps: {},
    transactionRequests: {},
}

export const onlyPKAccount: unknown = {
    selectedAddress: '******************************************',
    fetchedAt: '2023-03-23T09:11:42.229Z',
    accounts: {
        '******************************************': {
            address: '******************************************',
            label: 'Private Key 1',
            avatarSrc: null,
        },
    },
    keystoreMap: {
        '******************************************': {
            type: 'private_key_store',
            address: '******************************************',
            encryptedPrivateKey:
                '{"data":"6Gei4n/dvrvkUj1Oc7+EATwBO1UqKMu2xmEbO+RO/lEuAcI3IOdv8ryZRInAI4CnpYutGbUyVXiQI3ixqCciY3YQrLEnzl58kOfI30Qb4iWaoQ==","iv":"RHCBuQxECo/bj0Ix3u6xOA==","salt":"/m2J/GzEwlsZ7uzvJU05fpJ1CAxBlW5sWI9uhQIw9qQ="}',
        },
    },
    encryptedPassword:
        '{"data":"PbdGuBeWuHUclocd1EkI+3ol4RVMzWHAsPm43XbIK/2lFn4T/3TFGx2qO/3lDot81UQ2Yol8mcFWrYQbM5VBcvqplA==","iv":"nwDw7nqiYqeqZA91c6UcyA==","salt":"V8br/kvo1p/zmmKkF1pANz1QtWfCmpdqQ3NFN28sSBg="}',
    dApps: {},
    transactionRequests: {},
}

export const onlyPKAccountEmptyPortfolio: unknown = {
    selectedAddress: '******************************************',
    fetchedAt: '2023-03-23T09:11:42.229Z',
    accounts: {
        '******************************************': {
            address: '******************************************',
            label: 'Private Key 1',
            avatarSrc: null,
        },
    },
    keystoreMap: {
        '******************************************': {
            type: 'private_key_store',
            address: '******************************************',
            encryptedPrivateKey:
                '{"data":"6Gei4n/dvrvkUj1Oc7+EATwBO1UqKMu2xmEbO+RO/lEuAcI3IOdv8ryZRInAI4CnpYutGbUyVXiQI3ixqCciY3YQrLEnzl58kOfI30Qb4iWaoQ==","iv":"RHCBuQxECo/bj0Ix3u6xOA==","salt":"/m2J/GzEwlsZ7uzvJU05fpJ1CAxBlW5sWI9uhQIw9qQ="}',
        },
    },
    encryptedPassword:
        '{"data":"PbdGuBeWuHUclocd1EkI+3ol4RVMzWHAsPm43XbIK/2lFn4T/3TFGx2qO/3lDot81UQ2Yol8mcFWrYQbM5VBcvqplA==","iv":"nwDw7nqiYqeqZA91c6UcyA==","salt":"V8br/kvo1p/zmmKkF1pANz1QtWfCmpdqQ3NFN28sSBg="}',
    dApps: {},
    transactionRequests: {},
}

export const pendingBridge: unknown = {
    customNetworkMap: {},
    selectedAddress: '******************************************',
    fetchedAt: '2023-03-23T09:11:42.229Z',
    accounts: {
        '******************************************': {
            label: 'Account 1',
            address: '******************************************',
            avatarSrc: null,
        },
        '******************************************': {
            label: 'Private Key 1',
            address: '******************************************',
            avatarSrc: null,
        },
    },
    keystoreMap: {
        '******************************************': {
            type: 'private_key_store',
            address: '******************************************',
            encryptedPrivateKey:
                '{"data":"6Gei4n/dvrvkUj1Oc7+EATwBO1UqKMu2xmEbO+RO/lEuAcI3IOdv8ryZRInAI4CnpYutGbUyVXiQI3ixqCciY3YQrLEnzl58kOfI30Qb4iWaoQ==","iv":"RHCBuQxECo/bj0Ix3u6xOA==","salt":"/m2J/GzEwlsZ7uzvJU05fpJ1CAxBlW5sWI9uhQIw9qQ="}',
        },
    },
    encryptedPassword:
        '{"data":"PbdGuBeWuHUclocd1EkI+3ol4RVMzWHAsPm43XbIK/2lFn4T/3TFGx2qO/3lDot81UQ2Yol8mcFWrYQbM5VBcvqplA==","iv":"nwDw7nqiYqeqZA91c6UcyA==","salt":"V8br/kvo1p/zmmKkF1pANz1QtWfCmpdqQ3NFN28sSBg="}',
    customCurrencies: {},
    dApps: {},
    transactionRequests: {},
    submitedBridges: {
        '******************************************': [
            {
                submittedBridge: {
                    type: 'bridge_submitted',
                    sourceTransactionHash:
                        '0x1a44ec6f4652f4635064fee89516d22495d2827e8127c8a7afe2b67affca7913',
                    route: {
                        displayName: 'Across',
                        icon: 'https://miro.medium.com/max/800/1*PN_F5yW4VMBgs_xX-fsyzQ.png',
                        name: 'across',
                        serviceTimeMs: 180000,
                        from: {
                            amount: '157223311',
                            currency: {
                                type: 'CryptoCurrency',
                                id: 'Polygon|******************************************',
                                networkHexChainId: '0x89',
                                address:
                                    '******************************************',
                                symbol: 'USDC',
                                code: 'USDC',
                                fraction: 6,
                                rateFraction: 6,
                                icon: 'https://iw8i6d52oi.execute-api.eu-west-2.amazonaws.com/wallet/image/currency/Polygon|******************************************',
                                name: 'USD Coin (PoS)',
                            },
                        },
                        fromPriceInDefaultCurrency: {
                            amount: '157132120000000000000',
                            currency: {
                                type: 'FiatCurrency',
                                id: 'USD',
                                symbol: '$',
                                code: 'USD',
                                fraction: 18,
                                rateFraction: 18,
                                icon: 'TODO',
                                name: 'USD',
                            },
                        },
                        to: {
                            amount: '156857480',
                            currency: {
                                type: 'CryptoCurrency',
                                id: 'Arbitrum|******************************************',
                                networkHexChainId: '0xa4b1',
                                address:
                                    '******************************************',
                                symbol: 'USDT',
                                code: 'USDT',
                                fraction: 6,
                                rateFraction: 6,
                                icon: 'https://iw8i6d52oi.execute-api.eu-west-2.amazonaws.com/wallet/image/currency/Arbitrum|******************************************',
                                name: 'Tether USD',
                            },
                        },
                        toPriceInDefaultCurrency: {
                            amount: '156779050000000024576',
                            currency: {
                                type: 'FiatCurrency',
                                id: 'USD',
                                symbol: '$',
                                code: 'USD',
                                fraction: 18,
                                rateFraction: 18,
                                icon: 'TODO',
                                name: 'USD',
                            },
                        },
                        feeInDefaultCurrency: {
                            amount: '65980724898785272',
                            currency: {
                                type: 'FiatCurrency',
                                id: 'USD',
                                symbol: '$',
                                code: 'USD',
                                fraction: 18,
                                rateFraction: 18,
                                icon: 'TODO',
                                name: 'USD',
                            },
                        },
                        refuel: {
                            from: {
                                amount: '8701207697304016000',
                                currency: {
                                    type: 'CryptoCurrency',
                                    id: 'Polygon|******************************************',
                                    networkHexChainId: '0x89',
                                    address:
                                        '******************************************',
                                    symbol: 'MATIC',
                                    code: 'MATIC',
                                    fraction: 18,
                                    rateFraction: 18,
                                    icon: 'https://iw8i6d52oi.execute-api.eu-west-2.amazonaws.com/wallet/image/currency/Polygon|******************************************',
                                    name: 'MATIC',
                                },
                            },
                            to: {
                                amount: '3053246799776329',
                                currency: {
                                    type: 'CryptoCurrency',
                                    id: 'Arbitrum|******************************************',
                                    networkHexChainId: '0xa4b1',
                                    address:
                                        '******************************************',
                                    symbol: 'ETH',
                                    code: 'ETH',
                                    fraction: 18,
                                    rateFraction: 18,
                                    icon: 'https://iw8i6d52oi.execute-api.eu-west-2.amazonaws.com/wallet/image/currency/Arbitrum|******************************************',
                                    name: 'ETH',
                                },
                            },
                        },
                        approvalTransaction: {
                            id: 1854315410,
                            jsonrpc: '2.0',
                            method: 'eth_sendTransaction',
                            params: [
                                {
                                    data: '0x095ea7b30000000000000000000000003a23f943181408eac424116af7b7790c94cb97a500000000000000000000000000000000000000000000000000000000095f098f',
                                    from: '******************************************',
                                    to: '******************************************',
                                },
                            ],
                        },
                        sourceTransaction: {
                            id: 2684299939,
                            jsonrpc: '2.0',
                            method: 'eth_sendTransaction',
                            params: [
                                {
                                    data: '0x000000117d975f0b000000000000000000000000000000000000000000000000000000000000000900000000000000000000000000000000000000000000000000000000000000e000000000000000000000000061640a8d48bca205ba91b16b0b7745e7abc61084000000000000000000000000000000000000000000000000000000000000a4b1000000000000000000000000000000000000000000000000000000000000853000000000000000000000000000000000000000000000000000000188b0968c5e00000000000000000000000000000000000000000000000000000000000000cd00000000000000000000000000000000000000000000000000000000000002a4a2c7302c0000000000000000000000002791bca1f2de4661ed88a30c99a7a9449aa84174000000000000000000000000c2132d05d31c914a87c6611c10748aeb04b58e8f00000000000000000000000000000000000000000000000000000000095f098f000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000001e812aa3caf000000000000000000000000cfd674f8731e801a4a15c1ae31770960e1afded10000000000000000000000002791bca1f2de4661ed88a30c99a7a9449aa84174000000000000000000000000c2132d05d31c914a87c6611c10748aeb04b58e8f000000000000000000000000a0020444b98f67b77a3d6de6e66af11c87da086e0000000000000000000000003a23f943181408eac424116af7b7790c94cb97a500000000000000000000000000000000000000000000000000000000095f098f00000000000000000000000000000000000000000000000000000000095286350000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000014000000000000000000000000000000000000000000000000000000000000001600000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005800000000000000000000000000000000000000000000000000000000003a4020a0020444b98f67b77a3d6de6e66af11c87da086edd93f59a0000000000000000000000001111111254eeb25477b68fb85ed929f73a9605820000000000000000733d400400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000',
                                    to: '0x3a23F943181408EAC424116Af7b7790c94Cb97a5',
                                    from: '******************************************',
                                    value: '0x0',
                                },
                            ],
                        },
                    },
                    submittedAtMS: *************,
                    fromAddress: '******************************************',
                },
                dismissed: false,
            },
        ],
    },
    installationId: 'd60c14fb-571b-4b54-b036-3818b524816e',
    swapSlippagePercent: null,
}

export const pendingTransaction: unknown = {
    customNetworkMap: {},
    selectedAddress: '******************************************',
    fetchedAt: '2023-02-14T10:02:49.947Z',
    accounts: {
        '******************************************': {
            label: 'Account 1',
            address: '******************************************',
            avatarSrc: null,
        },
    },
    keystoreMap: {
        '******************************************': {
            type: 'secret_phrase_key',
            bip44Path: "m/44'/60'/0'/0/0",
            encryptedPhrase:
                '{"data":"dz8N2xQ89/PcrU2nJ4ylwoQ6afbnA3j2kzaq5bdakugQgmIWeZMf6wynYIVC35EX22tD1x74D3uEaUcCaCirPJZD6tAO84tQ4CZTZ2Z/ZRIXqsLX6KcEHqQVqbZn36Uqunpfvk4BQw==","iv":"AAECAwQFBgcICQoLDA0ODw==","salt":"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA="}',
            confirmed: false,
            googleDriveFile: null,
        },
    },
    encryptedPassword:
        '{"data":"8DBGNJMDoFGVa1M8g/hmjvMMKMEnI7WIiNqXAjpp10dELVvapXL4QnuQOcAAU3ag+1Vgd1nF8MdSBjsVRoUh91pMQ/FCI3NxEu0xcw0qaZijmNG0OK2VObgJfHl44hCKaNdXGT8tzttydJB3cYM5","iv":"AAECAwQFBgcICQoLDA0ODw==","salt":"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA="}',
    customCurrencies: {},
    dApps: {
        'dapp.example.com': {
            type: 'connected',
            dApp: {
                title: null,
                avatar: null,
                hostname: 'dapp.example.com',
            },
            networkHexId: '0x1',
            address: '******************************************',
            version: 1,
        },
    },
    transactionRequests: {
        '******************************************': [
            {
                state: 'submited',
                account: {
                    label: 'Account 1',
                    address: '******************************************',
                    avatarSrc: null,
                },
                dApp: {
                    title: null,
                    avatar: null,
                    hostname: 'dapp.example.com',
                },
                networkHexId: '0x1',
                rawTransaction: {
                    id: 123,
                    jsonrpc: '2.0',
                    method: 'eth_sendRawTransaction',
                    params: ['0x0'],
                },
                rpcRequest: {
                    id: 0,
                    jsonrpc: '2.0',
                    method: 'eth_sendTransaction',
                    params: [
                        {
                            from: '******************************************',
                            data: '0x095ea7b300000000000000000000000068b3465833fb72a70ecdf485e0e4c7bd8665fc45ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff',
                            to: '******************************************',
                            gas: '0x13466',
                        },
                    ],
                },
                simulation: {
                    type: 'simulated',
                    simulation: {
                        transaction: {
                            type: 'ApprovalTransaction',
                            amount: {
                                type: 'Unlimited',
                                amount: {
                                    amount: '115792089237316195423570985008687907853269984665640564039457584007913129639936',
                                    currencyId:
                                        'Polygon|******************************************',
                                },
                            },
                            simulatedGas: {
                                type: 'GasEstimate',
                                gas: '0x15675',
                            },
                            approveTo: {
                                address:
                                    '******************************************',
                                networkHexId: '0x89',
                            },
                        },
                        currencies: {
                            'Polygon|******************************************':
                                {
                                    type: 'CryptoCurrency',
                                    id: 'Polygon|******************************************',
                                    networkHexChainId: '0x89',
                                    address:
                                        '******************************************',
                                    symbol: 'MATIC',
                                    code: 'MATIC',
                                    fraction: 18,
                                    rateFraction: 18,
                                    icon: 'https://storage.googleapis.com/zapper-fi-assets/tokens/polygon/******************************************.png',
                                    name: 'Matic Token',
                                },
                            'Polygon|******************************************':
                                {
                                    type: 'CryptoCurrency',
                                    id: 'Polygon|******************************************',
                                    networkHexChainId: '0x89',
                                    address:
                                        '******************************************',
                                    symbol: 'USDC',
                                    code: 'USDC',
                                    fraction: 6,
                                    rateFraction: 6,
                                    icon: 'https://storage.googleapis.com/zapper-fi-assets/tokens/polygon/******************************************.png',
                                    name: 'USD Coin (PoS)',
                                },
                            USD: {
                                type: 'FiatCurrency',
                                id: 'USD',
                                symbol: '$',
                                code: 'USD',
                                fraction: 18,
                                rateFraction: 18,
                                icon: 'TODO',
                                name: 'USD',
                            },
                        },
                        checks: [
                            {
                                type: 'TransactionSimulationCheck',
                                state: 'Passed',
                                severity: 'Danger',
                                simulationUrl:
                                    'http://localhost/simulation-url',
                                checkSource: {
                                    source: 'Tenderly',
                                    url: null,
                                },
                            },
                            {
                                type: 'SmartContractBlacklistCheck',
                                state: 'Passed',
                                severity: 'Danger',
                                checkSource: {
                                    source: 'BlockAid',
                                    url: null,
                                },
                            },
                            {
                                type: 'TokenVerificationCheck',
                                state: 'Passed',
                                severity: 'Caution',
                                currencyId:
                                    'Polygon|******************************************',
                                checkSource: {
                                    source: 'CoinGecko',
                                    url: null,
                                },
                            },
                        ],
                    },
                },
                gasEstimate: '0x13466',
                selectedFee: {
                    type: 'Eip1559Fee',
                    maxPriorityFeePerGas: '0xaa4831d96',
                    maxFeePerGas: '0xc2d3fe437',
                    priceInDefaultCurrency: {
                        amount: '6315330743665099',
                        currency: {
                            type: 'FiatCurrency',
                            id: 'USD',
                            symbol: '$',
                            code: 'USD',
                            fraction: 18,
                            rateFraction: 18,
                            icon: 'TODO',
                            name: 'USD',
                        },
                    },
                    priceInNativeCurrency: {
                        amount: '6877497379462352',
                        currency: {
                            type: 'CryptoCurrency',
                            id: 'Polygon|******************************************',
                            networkHexChainId: '0x89',
                            address:
                                '******************************************',
                            symbol: 'MATIC',
                            code: 'MATIC',
                            fraction: 18,
                            rateFraction: 18,
                            icon: 'https://storage.googleapis.com/zapper-fi-assets/tokens/polygon/******************************************.png',
                            name: 'Matic Token',
                        },
                    },
                    maxPriceInDefaultCurrency: {
                        amount: '6415330743665099',
                        currency: {
                            type: 'FiatCurrency',
                            id: 'USD',
                            symbol: '$',
                            code: 'USD',
                            fraction: 18,
                            rateFraction: 18,
                            icon: 'TODO',
                            name: 'USD',
                        },
                    },
                    maxPriceInNativeCurrency: {
                        amount: '6977497379462352',
                        currency: {
                            type: 'CryptoCurrency',
                            id: 'Polygon|******************************************',
                            networkHexChainId: '0x89',
                            address:
                                '******************************************',
                            symbol: 'MATIC',
                            code: 'MATIC',
                            fraction: 18,
                            rateFraction: 18,
                            icon: 'https://storage.googleapis.com/zapper-fi-assets/tokens/polygon/******************************************.png',
                            name: 'Matic Token',
                        },
                    },
                    forecastDuration: {
                        type: 'WithinForecast',
                        durationMs: 4000,
                    },
                },
                selectedGas: '0x1ce99',
                selectedNonce: 127,
                submitedTransaction: {
                    state: 'queued',
                    hash: '0x1a44ec6f4652f4635064fee89516d22495d2827e8127c8a7afe2b67affca7913',
                    submittedNonce: 127,
                    queuedAt: *************,
                    senderAddress: '******************************************',
                },
            },
        ],
    },
    submitedBridges: {},
    installationId: '36c5d64e-23d3-4500-a81f-a12d545303bc',
    swapSlippagePercent: null,
}

export const bankTransferOnboarded: unknown = {
    currencyHiddenMap: {},
    currencyPinMap: {},
    isOnboardingStorySeen: false,
    feePresetMap: {},
    customNetworkMap: {},
    networkRPCMap: {},
    selectedAddress: '******************************************',
    fetchedAt: '2023-03-23T09:11:42.229Z',
    accounts: {
        '******************************************': {
            label: 'Private Key 1',
            address: '******************************************',
            avatarSrc: null,
        },
    },
    keystoreMap: {
        '******************************************': {
            type: 'private_key_store',
            address: '******************************************',
            encryptedPrivateKey:
                '{"data":"6Gei4n/dvrvkUj1Oc7+EATwBO1UqKMu2xmEbO+RO/lEuAcI3IOdv8ryZRInAI4CnpYutGbUyVXiQI3ixqCciY3YQrLEnzl58kOfI30Qb4iWaoQ==","iv":"RHCBuQxECo/bj0Ix3u6xOA==","salt":"/m2J/GzEwlsZ7uzvJU05fpJ1CAxBlW5sWI9uhQIw9qQ="}',
        },
    },
    encryptedPassword:
        '{"data":"PbdGuBeWuHUclocd1EkI+3ol4RVMzWHAsPm43XbIK/2lFn4T/3TFGx2qO/3lDot81UQ2Yol8mcFWrYQbM5VBcvqplA==","iv":"nwDw7nqiYqeqZA91c6UcyA==","salt":"V8br/kvo1p/zmmKkF1pANz1QtWfCmpdqQ3NFN28sSBg="}',
    customCurrencies: {},
    dApps: {},
    transactionRequests: {},
    submitedBridges: {},
    installationId: 'cf4633e5-7733-449f-937c-c9ce98480771',
    swapSlippagePercent: null,
    bankTransferInfo: {
        type: 'unblock_user_created',
        connectedWalletAddress: '******************************************',
        countryCode: 'GB',
        unblockUserId: 'xxx',
        unblockLoginSignature: {
            message:
                'api.getunblock.com wants you to sign in with your Ethereum account:\n******************************************\n\nSign in with Ethereum\n\nURI: https://api.getunblock.com/auth/login\nVersion: 1\nChain ID: 137\nNonce: **********\nIssued At: 2023-10-09T15:02:55.083Z\nExpiration Time: 2023-10-09T19:02:55.083Z',
            signature:
                '0x94286cc4a1f2a1230a112eee40116c1e7aa2a33b223f28f0a08c404173b28b806c88c3a719e8018345154cf6c702c0b8b700915ff893a7e27b4173578678e6121c',
            signal: {},
        },
        sumSubAccessToken: null,
    },
}
