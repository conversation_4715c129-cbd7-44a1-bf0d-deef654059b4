import { notReachable } from '@zeal/toolkit'
import * as Web3 from '@zeal/toolkit/Web3'

import { CardConfig } from '@zeal/domains/Card'
import { fetchTransactionsWithSilentLogin } from '@zeal/domains/Card/api/fetchTransactions'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'

import { CardTransactionActivity } from '..'

export const fetchCardTransactionActivities = async ({
    address,
    startTime,
    endTime,
    cardConfig,
    keyStoreMap,
    sessionPassword,
}: {
    address: Web3.address.Address
    startTime: Date | null
    endTime: Date | null
    cardConfig: CardConfig
    keyStoreMap: KeyStoreMap
    sessionPassword: string
}): Promise<CardTransactionActivity[]> => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
        case 'card_readonly_signer_address_is_selected':
            return []
        case 'card_readonly_signer_address_is_selected_fully_onboarded':
            if (cardConfig.readonlySignerAddress !== address) {
                return []
            }

            const keyStore = getKeyStore({
                address: cardConfig.readonlySignerAddress,
                keyStoreMap,
            })
            switch (keyStore.type) {
                case 'private_key_store':
                case 'secret_phrase_key':
                case 'safe_4337':
                    return fetchTransactionsWithSilentLogin({
                        keyStore,
                        readonlySignerAddress: cardConfig.readonlySignerAddress,
                        sessionPassword,
                        afterTimestampMs: endTime ? endTime.getTime() : endTime,
                        beforeTimestampMs: startTime
                            ? startTime.getTime()
                            : startTime,
                    })
                        .then((cardTransactions) =>
                            cardTransactions.map<CardTransactionActivity>(
                                (cardTransaction) => ({
                                    type: 'card_transaction',
                                    cardConfig,
                                    cardTransaction,
                                    timestamp: new Date(
                                        cardTransaction.createdAt
                                    ),
                                })
                            )
                        )
                        .catch((error) => {
                            const appError = parseAppError(error)
                            switch (appError.type) {
                                case 'gnosis_pay_is_not_available_in_this_country':
                                    return []

                                default:
                                    captureError(error)
                                    return []
                            }
                        })

                case 'ledger':
                case 'trezor':
                case 'track_only':
                    return []

                /* istanbul ignore next */
                default:
                    return notReachable(keyStore)
            }
        /* istanbul ignore next */
        default:
            return notReachable(cardConfig)
    }
}
