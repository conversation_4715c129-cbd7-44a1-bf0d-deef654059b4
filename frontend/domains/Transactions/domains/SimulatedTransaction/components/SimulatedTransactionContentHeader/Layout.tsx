import { FormattedMessage, useIntl } from 'react-intl'

import { Content } from '@zeal/uikit/Content'
import { InfoCircle } from '@zeal/uikit/Icon/InfoCircle'
import { IconButton } from '@zeal/uikit/IconButton'
import { Row } from '@zeal/uikit/Row'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'

import { convertStableCoinCurrencyToFiatCurrency } from '@zeal/domains/Currency/helpers/convertStableCoinCurrencyToFiatCurrency'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { SimulatedTransaction } from '@zeal/domains/Transactions/domains/SimulatedTransaction'

type Props = {
    simulatedTransaction: SimulatedTransaction
    dAppInfo: DAppSiteInfo | null
    onMsg: (msg: Msg) => void
}

type Msg = {
    type: 'on_approval_info_click'
}

export const Layout = ({ onMsg, simulatedTransaction, dAppInfo }: Props) => {
    const { formatMessage } = useIntl()

    switch (simulatedTransaction.type) {
        case 'earn_recharge_disabled':
            return (
                <Content.Header
                    title={
                        <FormattedMessage
                            id="earn.recharge_configured.disable.trx.title"
                            defaultMessage="Disable Auto-Recharge"
                        />
                    }
                />
            )
        case 'earn_recharge_updated':
            return (
                <Content.Header
                    title={
                        <FormattedMessage
                            id="earn.recharge_configured.updated.trx.title"
                            defaultMessage="Save Recharge Settings"
                        />
                    }
                />
            )

        case 'earn_recharge_configured':
            return (
                <Content.Header
                    title={
                        <FormattedMessage
                            id="earn.recharge_configured.trx.title"
                            defaultMessage="Set Auto-Recharge to {value}"
                            values={{
                                value: (
                                    <FormattedMoneyPrecise
                                        withSymbol
                                        sign={null}
                                        money={{
                                            currency:
                                                convertStableCoinCurrencyToFiatCurrency(
                                                    {
                                                        cryptoCurrency:
                                                            simulatedTransaction
                                                                .request
                                                                .cardConfig
                                                                .currency,
                                                    }
                                                ),
                                            amount: simulatedTransaction.request
                                                .threshold,
                                        }}
                                    />
                                ),
                            }}
                        />
                    }
                />
            )
        case 'earn_deposit_with_swap':
        case 'earn_deposit_direct_send':
            return (
                <Content.Header
                    title={
                        <FormattedMessage
                            id="earn.earn_deposit.trx.title"
                            defaultMessage="Deposit into Earn"
                        />
                    }
                />
            )
        case 'earn_withdraw':
            switch (simulatedTransaction.state) {
                case 'withdraw':
                    return (
                        <Content.Header
                            title={
                                <FormattedMessage
                                    id="earn.earn_withdraw.trx.title.withdrawal"
                                    defaultMessage="Withdraw from Earn"
                                />
                            }
                        />
                    )
                case 'approval':
                    return (
                        <Content.Header
                            title={
                                <FormattedMessage
                                    id="earn.earn_withdraw.trx.title.approval"
                                    defaultMessage="Approve withdrawal"
                                />
                            }
                        />
                    )
                case 'swap':
                    return (
                        <Content.Header
                            title={
                                <FormattedMessage
                                    id="earn.earn_withdraw.trx.title.withdraw_into_asset"
                                    defaultMessage="Withdraw into {asset}"
                                    values={{
                                        asset: simulatedTransaction.request
                                            .toAmount.currency.code,
                                    }}
                                />
                            }
                        />
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(simulatedTransaction.state)
            }

        case 'card_top_up_from_earn':
            switch (simulatedTransaction.state) {
                case 'withdraw':
                    return (
                        <Content.Header
                            title={
                                <FormattedMessage
                                    id="top-up-card-from-earn.trx.title.withdrawal"
                                    defaultMessage="Withdraw from Earn"
                                />
                            }
                        />
                    )
                case 'approval':
                    return (
                        <Content.Header
                            title={
                                <FormattedMessage
                                    id="top-up-card-from-earn.trx.title.approval"
                                    defaultMessage="Approve swap"
                                />
                            }
                        />
                    )
                case 'swap':
                    return (
                        <Content.Header
                            title={
                                <FormattedMessage
                                    id="top-up-card-from-earn.trx.title.swap"
                                    defaultMessage="Add to card"
                                />
                            }
                        />
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(simulatedTransaction.state)
            }

        case 'card_cashback_deposit':
            return (
                <Content.Header
                    title={
                        <FormattedMessage
                            id="cashback-deposit.trx.title"
                            defaultMessage="Deposit into Cashback"
                        />
                    }
                />
            )

        case 'deploy_earn_account':
            return (
                <Content.Header
                    title={
                        <FormattedMessage
                            id="earn.deploy.status.title"
                            defaultMessage="Create Earn account"
                        />
                    }
                />
            )

        case 'smart_wallet_activation':
            return (
                <Content.Header
                    title={
                        <FormattedMessage
                            id="activate-smart-wallet.title"
                            defaultMessage="Activate wallet"
                        />
                    }
                />
            )

        case 'WithdrawalTrx':
            return (
                <Content.Header
                    title={
                        <FormattedMessage
                            id="currency.bridge.withdrawal_status.title"
                            defaultMessage="Withdrawal"
                        />
                    }
                />
            )

        case 'BridgeTrx':
            return (
                <Content.Header
                    title={
                        <FormattedMessage
                            id="currency.bridge.bridge_status.title"
                            defaultMessage="Bridge"
                        />
                    }
                    subtitle={
                        <Text>
                            <FormattedMessage
                                id="currency.bridge.bridge_status.subtitle"
                                defaultMessage="Using {name}"
                                values={{
                                    name: simulatedTransaction.bridgeRoute
                                        .displayName,
                                }}
                            />
                        </Text>
                    }
                />
            )

        case 'FailedTransaction':
        case 'UnknownTransaction':
            return (
                <Content.Header
                    title={simulatedTransaction.method}
                    subtitle={
                        dAppInfo && (
                            <Text>
                                <FormattedMessage
                                    id="simulatedTransaction.unknown.using"
                                    defaultMessage="Using {app}"
                                    values={{
                                        app:
                                            dAppInfo?.title ||
                                            dAppInfo.hostname,
                                    }}
                                />
                            </Text>
                        )
                    }
                />
            )
        case 'ApprovalTransaction':
        case 'SingleNftApprovalTransaction':
        case 'NftCollectionApprovalTransaction':
            return (
                <Content.Header
                    title={
                        <Row spacing={16}>
                            <Text
                                variant="title3"
                                weight="semi_bold"
                                color="textPrimary"
                            >
                                <FormattedMessage
                                    id="simulatedTransaction.approval.title"
                                    defaultMessage="Approve"
                                />
                            </Text>
                            <IconButton
                                variant="on_light"
                                size="small"
                                aria-label={formatMessage({
                                    id: 'approval.what_are_approvals',
                                    defaultMessage: 'What are Approvals?',
                                })}
                                onClick={() =>
                                    onMsg({
                                        type: 'on_approval_info_click',
                                    })
                                }
                            >
                                {({ color }) => (
                                    <InfoCircle size={16} color={color} />
                                )}
                            </IconButton>
                        </Row>
                    }
                />
            )

        case 'swaps_io_native_token_swap':
            return (
                <Content.Header
                    title={
                        <FormattedMessage
                            id="currency.swaps_io_native_token_swap.title"
                            defaultMessage="Send"
                        />
                    }
                    subtitle={
                        <Text>
                            <FormattedMessage
                                id="currency.swaps_io_native_token_swap.subtitle"
                                defaultMessage="Using Swaps.IO"
                            />
                        </Text>
                    }
                />
            )

        case 'P2PTransaction':
        case 'P2PNftTransaction':
            return (
                <Content.Header
                    title={
                        <FormattedMessage
                            id="simulatedTransaction.P2PTransaction.info.title"
                            defaultMessage="Send"
                        />
                    }
                />
            )
        case 'CardTopUpTrx':
            return (
                <Content.Header
                    title={
                        <FormattedMessage
                            id="simulatedTransaction.CardTopUpTrx.info.title"
                            defaultMessage="Add cash to card"
                        />
                    }
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(simulatedTransaction)
    }
}
