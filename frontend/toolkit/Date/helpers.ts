// eslint-disable-next-line no-restricted-imports
import {
    add as dateFnsAdd,
    addDays as dateFnsAddDays,
    addMinutes as dateFnsAddMinutes,
    differenceInCalendarDays as dateFnsDifferenceInCalendarDays,
    differenceInYears as dateFnsDifferenceInYears,
    formatDistanceToNowStrict as dateFnsFormatDistanceToNowStrict,
    intervalToDuration as dateFnsIntervalToDuration,
    isThisMonth as dateFnsIsThisMonth,
    isToday as dateFnsIsToday,
    isValid,
    isYesterday as dateFnsIsYesterday,
    startOfDay as dateFnsStartOfDay,
    sub as dateFnsSub,
} from 'date-fns'

export const isValidDate = isValid
export const addMinutes = dateFnsAddMinutes
export const addDays = dateFnsAddDays
export const add = dateFnsAdd
export const sub = dateFnsSub
export const differenceInCalendarDays = dateFnsDifferenceInCalendarDays
export const differenceInYears = dateFnsDifferenceInYears
export const intervalToDuration = dateFnsIntervalToDuration
export const startOfDay = dateFnsStartOfDay
export const formatDistanceToNowStrict = dateFnsFormatDistanceToNowStrict
export const isThisMonth = dateFnsIsThisMonth
export const isToday = dateFnsIsToday
export const isYesterday = dateFnsIsYesterday
